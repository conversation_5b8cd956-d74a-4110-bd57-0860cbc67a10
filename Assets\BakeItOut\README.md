# Bake It Out - Project Structure

## Overview
"Bake It Out" is a first-person 3D bakery management game where players run and manage a bakery by completing orders, automating processes, and unlocking new recipes and technology.

## Folder Structure

### Scripts/
- **Core/**: Core game systems and base classes
- **Managers/**: Game managers (GameManager, OrderManager, etc.)
- **Equipment/**: Equipment-specific scripts (Oven, Mixer, etc.)
- **UI/**: User interface scripts
- **Multiplayer/**: Networking and multiplayer functionality
- **Data/**: Data structures and ScriptableObject definitions

### Prefabs/
- **Equipment/**: Kitchen equipment prefabs
- **UI/**: UI element prefabs
- **Ingredients/**: Ingredient and food item prefabs
- **Environment/**: Environmental objects and decorations

### ScriptableObjects/
- **Recipes/**: Recipe data assets
- **Ingredients/**: Ingredient data assets
- **Equipment/**: Equipment configuration data
- **Progression/**: Level and progression data

### Scenes/
- Game scenes (Main Bakery, Menus, etc.)

### Materials/
- Material assets for 3D objects

### Audio/
- **SFX/**: Sound effects
- **Music/**: Background music

### Textures/
- Texture assets

### Models/
- 3D model assets

## Key Features
- First-person perspective gameplay
- Equipment interaction system
- Order management and completion
- Player progression and leveling
- Recipe unlocking system
- Automation and technology upgrades
- Multiplayer cooperative gameplay
- Full controller support
