%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1085346d58b03be4798cf0f167eeef92, type: 3}
  m_Name: BasicOven
  m_EditorClassIdentifier: 
  equipmentName: Basic Oven
  description: 
  icon: {fileID: 0}
  prefab: {fileID: 0}
  equipmentType: 3
  maxLevel: 5
  baseProcessingSpeed: 1
  maxSimultaneousItems: 1
  canBeAutomated: 1
  automationUnlockLevel: 3
  automationCost: 500
  automationEfficiency: 0.8
  upgradeCosts:
  - level: 2
    cost: 100
    requiredMaterials: []
    materialQuantities: 
  - level: 3
    cost: 150
    requiredMaterials: []
    materialQuantities: 
  - level: 4
    cost: 225
    requiredMaterials: []
    materialQuantities: 
  - level: 5
    cost: 337.5
    requiredMaterials: []
    materialQuantities: 
  requiresPower: 0
  powerConsumption: 1
