%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd925a44dfcde2349a05491bba08e750, type: 3}
  m_Name: BasicBread
  m_EditorClassIdentifier: 
  recipeName: Basic Bread
  description: A simple, delicious bread perfect for any meal.
  icon: {fileID: 0}
  resultPrefab: {fileID: 0}
  requiredIngredients:
  - ingredient: {fileID: 0}
    quantity: 3
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 1
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 1
    minimumQuality: 1
  requiredEquipment: 3
  requiredPlayerLevel: 1
  preparationTime: 30
  cookingTime: 45
  steps:
  - stepDescription: Mix dry ingredients
    requiredEquipment: 2
    duration: 10
    requiresPlayerInput: 0
  - stepDescription: Add wet ingredients
    requiredEquipment: 2
    duration: 10
    requiresPlayerInput: 0
  - stepDescription: Knead dough
    requiredEquipment: 1
    duration: 10
    requiresPlayerInput: 0
  - stepDescription: Bake bread
    requiredEquipment: 3
    duration: 45
    requiresPlayerInput: 0
  baseExperience: 15
  basePrice: 8
  baseQuality: 2
  isUnlockedByDefault: 1
  unlockLevel: 1
  unlockCost: 0
  prerequisiteRecipes: []
