%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd925a44dfcde2349a05491bba08e750, type: 3}
  m_Name: StrawberryCake
  m_EditorClassIdentifier: 
  recipeName: Strawberry Cake
  description: A delightful cake with fresh strawberries.
  icon: {fileID: 0}
  resultPrefab: {fileID: 0}
  requiredIngredients:
  - ingredient: {fileID: 0}
    quantity: 3
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 2
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 2
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 2
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 2
    minimumQuality: 1
  - ingredient: {fileID: 0}
    quantity: 1
    minimumQuality: 1
  requiredEquipment: 3
  requiredPlayerLevel: 3
  preparationTime: 45
  cookingTime: 30
  steps:
  - stepDescription: Prepare strawberries
    requiredEquipment: 1
    duration: 10
    requiresPlayerInput: 0
  - stepDescription: Cream butter and sugar
    requiredEquipment: 2
    duration: 8
    requiresPlayerInput: 0
  - stepDescription: Add eggs and vanilla
    requiredEquipment: 2
    duration: 5
    requiresPlayerInput: 0
  - stepDescription: Mix in flour
    requiredEquipment: 2
    duration: 7
    requiresPlayerInput: 0
  - stepDescription: Fold in strawberries
    requiredEquipment: 1
    duration: 5
    requiresPlayerInput: 0
  - stepDescription: Bake cake
    requiredEquipment: 3
    duration: 30
    requiresPlayerInput: 0
  baseExperience: 25
  basePrice: 20
  baseQuality: 2
  isUnlockedByDefault: 0
  unlockLevel: 3
  unlockCost: 0
  prerequisiteRecipes: []
