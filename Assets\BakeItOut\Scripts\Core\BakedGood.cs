using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;

namespace BakeItOut.Core
{
    /// <summary>
    /// Component for finished baked goods that can be picked up and used for orders
    /// </summary>
    public class BakedGood : InteractableBase
    {
        [Header("Baked Good Data")]
        public RecipeData recipe;
        public Data.QualityLevel quality = Data.QualityLevel.Common;
        public float freshness = 1.0f; // 1.0 = completely fresh, 0.0 = stale
        public float spoilRate = 0.1f; // How fast it spoils (per minute)
        
        [Header("Visual Quality Indicators")]
        public Material[] qualityMaterials; // Different materials for different qualities
        public ParticleSystem qualityParticles;
        public GameObject[] qualityDecorations;
        
        [Header("Pickup Settings")]
        public bool canBePickedUp = true;
        public float pickupDelay = 0.5f; // Delay before item can be picked up after creation
        
        private float creationTime;
        private bool isInitialized = false;
        private Renderer itemRenderer;
        
        private void Start()
        {
            itemRenderer = GetComponent<Renderer>();
            creationTime = Time.time;

            if (!isInitialized)
            {
                // If not initialized through Initialize method, use default values
                Initialize(recipe, quality);
            }
        }
        
        private void Update()
        {
            UpdateFreshness();
            UpdateVisualQuality();
        }
        
        public void Initialize(RecipeData recipeData, Data.QualityLevel itemQuality)
        {
            recipe = recipeData;
            quality = itemQuality;
            freshness = 1.0f;
            creationTime = Time.time;
            isInitialized = true;
            
            // Set interaction prompt
            if (recipe != null)
            {
                interactionPrompt = $"Pick up {recipe.recipeName}";
            }
            
            // Apply visual quality
            ApplyQualityVisuals();
        }
        
        private void UpdateFreshness()
        {
            if (!isInitialized) return;
            
            // Decrease freshness over time
            float timeElapsed = (Time.time - creationTime) / 60f; // Convert to minutes
            freshness = Mathf.Max(0f, 1f - (timeElapsed * spoilRate));
            
            // If completely spoiled, destroy or mark as spoiled
            if (freshness <= 0f)
            {
                OnSpoiled();
            }
        }
        
        private void OnSpoiled()
        {
            // Mark as spoiled and change appearance
            canBePickedUp = false;
            interactionPrompt = "Spoiled food - dispose of it";
            
            // Change visual appearance to indicate spoilage
            if (itemRenderer != null)
            {
                itemRenderer.material.color = Color.gray;
            }
            
            // Could add spoilage particles or other effects here
        }
        
        private void ApplyQualityVisuals()
        {
            if (!isInitialized) return;
            
            // Apply quality material
            if (qualityMaterials != null && qualityMaterials.Length > 0 && itemRenderer != null)
            {
                int materialIndex = Mathf.Clamp((int)quality - 1, 0, qualityMaterials.Length - 1);
                if (qualityMaterials[materialIndex] != null)
                {
                    itemRenderer.material = qualityMaterials[materialIndex];
                }
            }
            
            // Apply quality particles
            if (qualityParticles != null && quality >= Data.QualityLevel.Good)
            {
                qualityParticles.Play();
                
                // Adjust particle intensity based on quality
                var main = qualityParticles.main;
                main.startLifetime = (float)quality * 0.5f;
            }
            
            // Show/hide quality decorations
            if (qualityDecorations != null)
            {
                for (int i = 0; i < qualityDecorations.Length; i++)
                {
                    if (qualityDecorations[i] != null)
                    {
                        // Show decoration if quality is high enough
                        bool shouldShow = (int)quality > i + 2; // Show for Good+ quality
                        qualityDecorations[i].SetActive(shouldShow);
                    }
                }
            }
        }
        
        private void UpdateVisualQuality()
        {
            if (!isInitialized || itemRenderer == null) return;
            
            // Adjust color based on freshness
            Color baseColor = itemRenderer.material.color;
            Color freshnessColor = Color.Lerp(Color.gray, baseColor, freshness);
            
            // Don't override the material, just tint it
            if (itemRenderer.material.HasProperty("_Color"))
            {
                itemRenderer.material.color = freshnessColor;
            }
        }
        
        public override bool CanInteract(GameObject player)
        {
            if (!base.CanInteract(player) || !canBePickedUp)
                return false;
            
            // Check pickup delay
            if (Time.time - creationTime < pickupDelay)
                return false;
            
            // Check if player has inventory space
            var inventoryManager = GameManager.Instance?.InventoryManager;
            if (inventoryManager != null)
            {
                // For now, assume baked goods don't go into ingredient inventory
                // They might go into a separate finished goods inventory
                return true;
            }
            
            return true;
        }
        
        public override void Interact(GameObject player)
        {
            if (!CanInteract(player))
                return;
            
            // Pick up the baked good
            PickUp(player);
        }
        
        private void PickUp(GameObject player)
        {
            // TODO: Add to player's finished goods inventory or hand
            // For now, just log and destroy
            
            Debug.Log($"Picked up {recipe.recipeName} (Quality: {quality}, Freshness: {freshness:F2})");
            
            // Could trigger pickup effects here
            if (qualityParticles != null)
            {
                qualityParticles.Stop();
            }
            
            // Notify any systems that might care about this pickup
            OnPickedUp(player);
            
            // Remove from scene
            Destroy(gameObject);
        }
        
        private void OnPickedUp(GameObject player)
        {
            // This could be used to update order completion, statistics, etc.
            var orderManager = GameManager.Instance?.OrderManager;
            if (orderManager != null)
            {
                // Check if this item completes any active orders
                CheckOrderCompletion();
            }
        }
        
        private void CheckOrderCompletion()
        {
            var orderManager = GameManager.Instance?.OrderManager;
            if (orderManager == null || recipe == null) return;
            
            // Check active orders for matching items
            foreach (var activeOrder in orderManager.activeOrders)
            {
                foreach (var requiredItem in activeOrder.orderData.requiredItems)
                {
                    if (requiredItem.recipe == recipe && 
                        quality >= activeOrder.orderData.minimumQuality)
                    {
                        // This item could fulfill part of an order
                        // TODO: Implement proper order fulfillment system
                        Debug.Log($"Item {recipe.recipeName} could fulfill order from {activeOrder.orderData.customerName}");
                    }
                }
            }
        }
        
        public override string GetInteractionPrompt()
        {
            if (!canBePickedUp)
                return "Cannot pick up";
            
            if (Time.time - creationTime < pickupDelay)
                return "Still cooling down...";
            
            if (freshness < 0.3f)
                return $"Pick up {recipe?.recipeName} (Getting stale)";
            
            return $"Pick up {recipe?.recipeName} ({quality})";
        }
        
        // Public getters
        public RecipeData GetRecipe() => recipe;
        public Data.QualityLevel GetQuality() => quality;
        public float GetFreshness() => freshness;
        public bool IsFresh() => freshness > 0.7f;
        public bool IsStale() => freshness < 0.3f;
        public bool IsSpoiled() => freshness <= 0f;
        
        // Method to manually set freshness (for testing or special cases)
        public void SetFreshness(float newFreshness)
        {
            freshness = Mathf.Clamp01(newFreshness);
        }
    }
}
