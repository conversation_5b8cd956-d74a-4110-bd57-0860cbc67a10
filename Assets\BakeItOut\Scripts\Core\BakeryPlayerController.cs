using UnityEngine;
using StarterAssets;
using BakeItOut.UI;

#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

namespace BakeItOut.Core
{
    /// <summary>
    /// Extended FirstPersonController for bakery-specific functionality
    /// </summary>
    [RequireComponent(typeof(FirstPersonController))]
    [RequireComponent(typeof(InteractionSystem))]
    public class BakeryPlayerController : MonoBehaviour
    {
        [Header("Bakery Controls")]
        public bool bakeryInputEnabled = true;
        
        // Input values for bakery actions
        [Header("Bakery Input Values")]
        public bool interact;
        public bool openInventory;
        public bool openRecipeBook;
        public bool quickAction;
        public bool dropItem;
        public bool pauseGame;
        
        // Component references
        private FirstPersonController firstPersonController;
        private InteractionSystem interactionSystem;
        private UIManager uiManager;
        
        private void Start()
        {
            // Get component references
            firstPersonController = GetComponent<FirstPersonController>();
            interactionSystem = GetComponent<InteractionSystem>();
            uiManager = FindFirstObjectByType<UIManager>();
            
            // Subscribe to interaction events
            if (interactionSystem != null)
            {
                interactionSystem.OnInteractionPerformed += OnInteractionPerformed;
            }
        }
        
        private void Update()
        {
            if (!bakeryInputEnabled) return;
            
            HandleBakeryInputs();
        }
        
        private void HandleBakeryInputs()
        {
            // Handle interaction
            if (interact && interactionSystem != null)
            {
                interactionSystem.ForceInteraction();
                interact = false; // Reset after use
            }
            
            // Handle UI toggles
            if (openInventory && uiManager != null)
            {
                uiManager.ToggleInventory();
                openInventory = false;
            }
            
            if (openRecipeBook && uiManager != null)
            {
                uiManager.ToggleRecipeBook();
                openRecipeBook = false;
            }
            
            if (pauseGame && uiManager != null)
            {
                uiManager.TogglePauseMenu();
                pauseGame = false;
            }
            
            // Handle quick action (context-sensitive)
            if (quickAction)
            {
                HandleQuickAction();
                quickAction = false;
            }
            
            // Handle drop item
            if (dropItem)
            {
                HandleDropItem();
                dropItem = false;
            }
        }
        
        private void HandleQuickAction()
        {
            // Quick action is context-sensitive based on what the player is looking at
            if (interactionSystem != null && interactionSystem.HasInteractionTarget())
            {
                // If looking at something interactable, interact with it
                interactionSystem.ForceInteraction();
            }
            else
            {
                // Otherwise, perform a default quick action (e.g., open nearest recipe)
                var recipeManager = GameManager.Instance?.RecipeManager;
                if (recipeManager != null)
                {
                    var availableRecipes = recipeManager.GetMakeableRecipes();
                    if (availableRecipes.Count > 0)
                    {
                        // Start the first available recipe (this could be improved with a selection system)
                        recipeManager.StartRecipe(availableRecipes[0]);
                    }
                }
            }
        }
        
        private void HandleDropItem()
        {
            // TODO: Implement item dropping logic
            // This would interact with the inventory system to drop the currently held item
            Debug.Log("Drop item functionality - to be implemented");
        }
        
        private void OnInteractionPerformed(IInteractable interactable)
        {
            // Handle any post-interaction logic here
            Debug.Log($"Interacted with: {interactable.GetType().Name}");
        }
        
        // Input System callbacks for bakery actions
        #if ENABLE_INPUT_SYSTEM
        public void OnInteract(InputValue value)
        {
            if (bakeryInputEnabled)
                InteractInput(value.isPressed);
        }
        
        public void OnOpenInventory(InputValue value)
        {
            if (bakeryInputEnabled && value.isPressed)
                OpenInventoryInput(true);
        }
        
        public void OnOpenRecipeBook(InputValue value)
        {
            if (bakeryInputEnabled && value.isPressed)
                OpenRecipeBookInput(true);
        }
        
        public void OnQuickAction(InputValue value)
        {
            if (bakeryInputEnabled && value.isPressed)
                QuickActionInput(true);
        }
        
        public void OnDropItem(InputValue value)
        {
            if (bakeryInputEnabled && value.isPressed)
                DropItemInput(true);
        }
        
        public void OnPause(InputValue value)
        {
            if (bakeryInputEnabled && value.isPressed)
                PauseInput(true);
        }
        #endif
        
        // Input methods
        public void InteractInput(bool newInteractState)
        {
            interact = newInteractState;
        }
        
        public void OpenInventoryInput(bool newState)
        {
            openInventory = newState;
        }
        
        public void OpenRecipeBookInput(bool newState)
        {
            openRecipeBook = newState;
        }
        
        public void QuickActionInput(bool newState)
        {
            quickAction = newState;
        }
        
        public void DropItemInput(bool newState)
        {
            dropItem = newState;
        }
        
        public void PauseInput(bool newState)
        {
            pauseGame = newState;
        }
        
        // Public methods for external control
        public void EnableBakeryInput()
        {
            bakeryInputEnabled = true;
        }
        
        public void DisableBakeryInput()
        {
            bakeryInputEnabled = false;
        }
        
        public void SetInputEnabled(bool enabled)
        {
            bakeryInputEnabled = enabled;
            
            // Also control the base FirstPersonController if needed
            if (firstPersonController != null)
            {
                firstPersonController.enabled = enabled;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (interactionSystem != null)
            {
                interactionSystem.OnInteractionPerformed -= OnInteractionPerformed;
            }
        }
    }
}
