using UnityEngine;

namespace BakeItOut.Core
{
    /// <summary>
    /// Simple utility to create a floor for the bakery
    /// </summary>
    public class FloorCreator : MonoBehaviour
    {
        [Header("Floor Settings")]
        public Vector3 floorSize = new Vector3(20f, 1f, 15f);
        public Color floorColor = new Color(0.8f, 0.7f, 0.6f); // Light brown
        public bool createOnStart = true;
        
        [Header("Material Settings")]
        public float metallic = 0f;
        public float smoothness = 0.2f;
        
        private void Start()
        {
            if (createOnStart)
            {
                CreateFloor();
            }
        }
        
        [ContextMenu("Create Floor")]
        public void CreateFloor()
        {
            // Check if floor already exists
            GameObject existingFloor = GameObject.Find("Floor");
            if (existingFloor != null)
            {
                Debug.Log("Floor already exists! Updating existing floor.");
                UpdateFloor(existingFloor);
                return;
            }
            
            // Create new floor
            GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
            floor.name = "Floor";
            
            // Position and scale the floor
            floor.transform.position = Vector3.zero;
            floor.transform.localScale = new Vector3(floorSize.x / 10f, 1f, floorSize.z / 10f);
            
            // Create and apply material
            ApplyFloorMaterial(floor);
            
            Debug.Log($"Floor created! Size: {floorSize.x}x{floorSize.z}");
        }
        
        private void UpdateFloor(GameObject floor)
        {
            // Update scale
            floor.transform.localScale = new Vector3(floorSize.x / 10f, 1f, floorSize.z / 10f);
            
            // Update material
            ApplyFloorMaterial(floor);
        }
        
        private void ApplyFloorMaterial(GameObject floor)
        {
            var renderer = floor.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Create a new material
                Material floorMaterial = new Material(Shader.Find("Standard"));
                floorMaterial.color = floorColor;
                floorMaterial.metallic = metallic;
                floorMaterial.smoothness = smoothness;
                
                renderer.material = floorMaterial;
            }
        }
        
        [ContextMenu("Create Walls")]
        public void CreateWalls()
        {
            float halfWidth = floorSize.x / 2f;
            float halfDepth = floorSize.z / 2f;
            float height = 3f; // Default wall height
            
            // Front wall
            CreateWall("Front Wall", new Vector3(0, height / 2f, halfDepth), new Vector3(floorSize.x, height, 0.2f));
            
            // Back wall
            CreateWall("Back Wall", new Vector3(0, height / 2f, -halfDepth), new Vector3(floorSize.x, height, 0.2f));
            
            // Left wall
            CreateWall("Left Wall", new Vector3(-halfWidth, height / 2f, 0), new Vector3(0.2f, height, floorSize.z));
            
            // Right wall
            CreateWall("Right Wall", new Vector3(halfWidth, height / 2f, 0), new Vector3(0.2f, height, floorSize.z));
            
            Debug.Log("Walls created!");
        }
        
        private void CreateWall(string name, Vector3 position, Vector3 scale)
        {
            // Check if wall already exists
            GameObject existingWall = GameObject.Find(name);
            if (existingWall != null)
            {
                existingWall.transform.position = position;
                existingWall.transform.localScale = scale;
                return;
            }
            
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = name;
            wall.transform.position = position;
            wall.transform.localScale = scale;
            
            var renderer = wall.GetComponent<Renderer>();
            if (renderer != null)
            {
                Material wallMaterial = new Material(Shader.Find("Standard"));
                wallMaterial.color = new Color(0.9f, 0.9f, 0.8f); // Off-white
                wallMaterial.metallic = 0f;
                wallMaterial.smoothness = 0.1f;
                renderer.material = wallMaterial;
            }
        }
        
        [ContextMenu("Create Complete Environment")]
        public void CreateCompleteEnvironment()
        {
            CreateFloor();
            CreateWalls();
            
            // Add some basic lighting if none exists
            if (FindFirstObjectByType<Light>() == null)
            {
                CreateBasicLighting();
            }
        }
        
        private void CreateBasicLighting()
        {
            // Create main directional light
            GameObject lightObj = new GameObject("Main Light");
            Light mainLight = lightObj.AddComponent<Light>();
            mainLight.type = LightType.Directional;
            mainLight.color = Color.white;
            mainLight.intensity = 1.2f;
            mainLight.transform.rotation = Quaternion.Euler(45f, 45f, 0f);
            
            // Set ambient lighting
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
            RenderSettings.ambientLight = Color.gray * 0.3f;
            
            Debug.Log("Basic lighting created!");
        }
        
        [ContextMenu("Remove Floor")]
        public void RemoveFloor()
        {
            GameObject floor = GameObject.Find("Floor");
            if (floor != null)
            {
                DestroyImmediate(floor);
                Debug.Log("Floor removed!");
            }
            else
            {
                Debug.Log("No floor found to remove.");
            }
        }
        
        [ContextMenu("Remove Walls")]
        public void RemoveWalls()
        {
            string[] wallNames = { "Front Wall", "Back Wall", "Left Wall", "Right Wall" };
            int removedCount = 0;
            
            foreach (string wallName in wallNames)
            {
                GameObject wall = GameObject.Find(wallName);
                if (wall != null)
                {
                    DestroyImmediate(wall);
                    removedCount++;
                }
            }
            
            Debug.Log($"Removed {removedCount} walls.");
        }
    }
}
