using UnityEngine;
using BakeItOut.Data;
using System.Collections.Generic;

namespace BakeItOut.Core
{
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public PlayerProgressionData progressionData;
        public bool isMultiplayer = false;
        public int maxPlayers = 4;
        
        [Header("Game State")]
        public GameState currentState = GameState.Playing;
        public float gameTime = 0f;
        public bool isPaused = false;
        
        // Singleton instance
        public static GameManager Instance { get; private set; }
        
        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<bool> OnPauseStateChanged;
        public System.Action<float> OnGameTimeUpdated;
        
        // Managers
        public PlayerManager PlayerManager { get; private set; }
        public OrderManager OrderManager { get; private set; }
        public InventoryManager InventoryManager { get; private set; }
        public RecipeManager RecipeManager { get; private set; }
        public EquipmentManager EquipmentManager { get; private set; }
        public UIManager UIManager { get; private set; }
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeManagers();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            StartGame();
        }
        
        private void Update()
        {
            if (!isPaused && currentState == GameState.Playing)
            {
                gameTime += Time.deltaTime;
                OnGameTimeUpdated?.Invoke(gameTime);
            }
        }
        
        private void InitializeManagers()
        {
            // Initialize all manager components
            PlayerManager = GetComponent<PlayerManager>() ?? gameObject.AddComponent<PlayerManager>();
            OrderManager = GetComponent<OrderManager>() ?? gameObject.AddComponent<OrderManager>();
            InventoryManager = GetComponent<InventoryManager>() ?? gameObject.AddComponent<InventoryManager>();
            RecipeManager = GetComponent<RecipeManager>() ?? gameObject.AddComponent<RecipeManager>();
            EquipmentManager = GetComponent<EquipmentManager>() ?? gameObject.AddComponent<EquipmentManager>();
            UIManager = FindObjectOfType<UIManager>();
        }
        
        public void StartGame()
        {
            ChangeGameState(GameState.Playing);
            
            // Initialize player data
            PlayerManager.InitializePlayer(progressionData);
            
            // Start order generation
            OrderManager.StartOrderGeneration();
            
            Debug.Log("Bake It Out - Game Started!");
        }
        
        public void PauseGame()
        {
            isPaused = true;
            Time.timeScale = 0f;
            OnPauseStateChanged?.Invoke(true);
        }
        
        public void ResumeGame()
        {
            isPaused = false;
            Time.timeScale = 1f;
            OnPauseStateChanged?.Invoke(false);
        }
        
        public void TogglePause()
        {
            if (isPaused)
                ResumeGame();
            else
                PauseGame();
        }
        
        public void ChangeGameState(GameState newState)
        {
            if (currentState != newState)
            {
                currentState = newState;
                OnGameStateChanged?.Invoke(newState);
                Debug.Log($"Game state changed to: {newState}");
            }
        }
        
        public void SaveGame()
        {
            // TODO: Implement save system
            Debug.Log("Game saved!");
        }
        
        public void LoadGame()
        {
            // TODO: Implement load system
            Debug.Log("Game loaded!");
        }
        
        public void QuitGame()
        {
            SaveGame();
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.Playing)
            {
                PauseGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.Playing)
            {
                PauseGame();
            }
        }
    }
    
    public enum GameState
    {
        MainMenu,
        Loading,
        Playing,
        Paused,
        GameOver,
        Victory
    }
}
