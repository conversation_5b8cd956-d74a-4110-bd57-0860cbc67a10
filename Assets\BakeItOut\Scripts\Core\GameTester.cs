using UnityEngine;
using BakeItOut.Core;
using BakeItOut.Data;
using System.Collections;

namespace BakeItOut.Core
{
    /// <summary>
    /// Test script to verify game systems are working correctly
    /// </summary>
    public class GameTester : MonoBehaviour
    {
        [Header("Testing")]
        public bool runTestsOnStart = false;
        public bool enableDebugLogs = true;
        
        [Header("Test Controls")]
        public KeyCode testInventoryKey = KeyCode.F1;
        public KeyCode testOrderKey = KeyCode.F2;
        public KeyCode testRecipeKey = KeyCode.F3;
        public KeyCode testLevelUpKey = KeyCode.F4;
        public KeyCode addMoneyKey = KeyCode.F5;
        
        private void Start()
        {
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllTests());
            }
        }
        
        private void Update()
        {
            HandleTestInputs();
        }
        
        private void HandleTestInputs()
        {
            if (Input.GetKeyDown(testInventoryKey))
            {
                TestInventorySystem();
            }
            
            if (Input.GetKeyDown(testOrderKey))
            {
                TestOrderSystem();
            }
            
            if (Input.GetKeyDown(testRecipeKey))
            {
                TestRecipeSystem();
            }
            
            if (Input.GetKeyDown(testLevelUpKey))
            {
                TestLevelUp();
            }
            
            if (Input.GetKeyDown(addMoneyKey))
            {
                AddTestMoney();
            }
        }
        
        private IEnumerator RunAllTests()
        {
            DebugLog("Starting comprehensive game tests...");
            
            yield return new WaitForSeconds(1f); // Wait for initialization
            
            TestGameManagerInitialization();
            yield return new WaitForSeconds(0.5f);
            
            TestPlayerManager();
            yield return new WaitForSeconds(0.5f);
            
            TestInventorySystem();
            yield return new WaitForSeconds(0.5f);
            
            TestOrderSystem();
            yield return new WaitForSeconds(0.5f);
            
            TestRecipeSystem();
            yield return new WaitForSeconds(0.5f);
            
            TestEquipmentSystem();
            yield return new WaitForSeconds(0.5f);
            
            DebugLog("All tests completed!");
        }
        
        private void TestGameManagerInitialization()
        {
            DebugLog("Testing GameManager initialization...");
            
            var gameManager = GameManager.Instance;
            if (gameManager == null)
            {
                DebugLog("ERROR: GameManager not found!");
                return;
            }
            
            DebugLog($"GameManager found. Current state: {gameManager.currentState}");
            DebugLog($"Is multiplayer: {gameManager.isMultiplayer}");
            
            // Test manager references
            if (gameManager.PlayerManager != null)
                DebugLog("✓ PlayerManager initialized");
            else
                DebugLog("✗ PlayerManager missing");
            
            if (gameManager.InventoryManager != null)
                DebugLog("✓ InventoryManager initialized");
            else
                DebugLog("✗ InventoryManager missing");
            
            if (gameManager.OrderManager != null)
                DebugLog("✓ OrderManager initialized");
            else
                DebugLog("✗ OrderManager missing");
            
            if (gameManager.RecipeManager != null)
                DebugLog("✓ RecipeManager initialized");
            else
                DebugLog("✗ RecipeManager missing");
            
            if (gameManager.EquipmentManager != null)
                DebugLog("✓ EquipmentManager initialized");
            else
                DebugLog("✗ EquipmentManager missing");
        }
        
        private void TestPlayerManager()
        {
            DebugLog("Testing PlayerManager...");
            
            var playerManager = GameManager.Instance?.PlayerManager;
            if (playerManager == null)
            {
                DebugLog("ERROR: PlayerManager not found!");
                return;
            }
            
            DebugLog($"Player Level: {playerManager.currentLevel}");
            DebugLog($"Player Experience: {playerManager.currentExperience}");
            DebugLog($"Player Money: ${playerManager.currentMoney:F2}");
            DebugLog($"Unlocked Recipes: {playerManager.unlockedRecipes.Count}");
            DebugLog($"Unlocked Equipment: {playerManager.unlockedEquipment.Count}");
            DebugLog($"Researched Technologies: {playerManager.researchedTechnologies.Count}");
        }
        
        private void TestInventorySystem()
        {
            DebugLog("Testing Inventory System...");
            
            var inventoryManager = GameManager.Instance?.InventoryManager;
            if (inventoryManager == null)
            {
                DebugLog("ERROR: InventoryManager not found!");
                return;
            }
            
            DebugLog($"Inventory slots used: {inventoryManager.GetUsedSlots()}/{inventoryManager.maxInventorySlots}");
            DebugLog($"Total items: {inventoryManager.GetTotalItemCount()}");
            
            // Test adding some sample ingredients
            var sampleCreator = FindFirstObjectByType<SampleDataCreator>();
            if (sampleCreator != null)
            {
                // Create some test ingredients
                var flour = ScriptableObject.CreateInstance<IngredientData>();
                flour.ingredientName = "Test Flour";
                flour.type = IngredientType.Flour;
                flour.stackSize = 50;
                
                bool added = inventoryManager.AddIngredient(flour, 10, QualityLevel.Common);
                DebugLog($"Added test flour: {added}");
                
                if (added)
                {
                    int count = inventoryManager.GetIngredientCount(flour);
                    DebugLog($"Flour count in inventory: {count}");
                }
            }
        }
        
        private void TestOrderSystem()
        {
            DebugLog("Testing Order System...");
            
            var orderManager = GameManager.Instance?.OrderManager;
            if (orderManager == null)
            {
                DebugLog("ERROR: OrderManager not found!");
                return;
            }
            
            DebugLog($"Active orders: {orderManager.activeOrders.Count}");
            DebugLog($"Max orders: {orderManager.maxActiveOrders}");
            
            // Start order generation if not already started
            if (orderManager.activeOrders.Count == 0)
            {
                orderManager.StartOrderGeneration();
                DebugLog("Started order generation");
            }
        }
        
        private void TestRecipeSystem()
        {
            DebugLog("Testing Recipe System...");
            
            var recipeManager = GameManager.Instance?.RecipeManager;
            if (recipeManager == null)
            {
                DebugLog("ERROR: RecipeManager not found!");
                return;
            }
            
            var availableRecipes = recipeManager.GetAvailableRecipes();
            DebugLog($"Available recipes: {availableRecipes.Count}");
            
            var makeableRecipes = recipeManager.GetMakeableRecipes();
            DebugLog($"Makeable recipes: {makeableRecipes.Count}");
            
            var activeRecipes = recipeManager.GetAllActiveRecipes();
            DebugLog($"Active recipes: {activeRecipes.Count}");
        }
        
        private void TestEquipmentSystem()
        {
            DebugLog("Testing Equipment System...");
            
            var equipmentManager = GameManager.Instance?.EquipmentManager;
            if (equipmentManager == null)
            {
                DebugLog("ERROR: EquipmentManager not found!");
                return;
            }
            
            var allEquipment = equipmentManager.GetAllEquipment();
            DebugLog($"Total equipment instances: {allEquipment.Count}");
            
            var automatedEquipment = equipmentManager.GetAutomatedEquipment();
            DebugLog($"Automated equipment: {automatedEquipment.Count}");
            
            // Test equipment by type
            var ovens = equipmentManager.GetEquipmentByType(EquipmentType.Oven);
            var mixers = equipmentManager.GetEquipmentByType(EquipmentType.Mixer);
            var prepStations = equipmentManager.GetEquipmentByType(EquipmentType.PrepStation);
            
            DebugLog($"Ovens: {ovens.Count}, Mixers: {mixers.Count}, Prep Stations: {prepStations.Count}");
        }
        
        private void TestLevelUp()
        {
            DebugLog("Testing level up...");
            
            var playerManager = GameManager.Instance?.PlayerManager;
            if (playerManager != null)
            {
                int oldLevel = playerManager.currentLevel;
                playerManager.GainExperience(100);
                int newLevel = playerManager.currentLevel;
                
                DebugLog($"Level changed from {oldLevel} to {newLevel}");
            }
        }
        
        private void AddTestMoney()
        {
            DebugLog("Adding test money...");
            
            var playerManager = GameManager.Instance?.PlayerManager;
            if (playerManager != null)
            {
                float oldMoney = playerManager.currentMoney;
                playerManager.AddMoney(100f);
                float newMoney = playerManager.currentMoney;
                
                DebugLog($"Money changed from ${oldMoney:F2} to ${newMoney:F2}");
            }
        }
        
        private void DebugLog(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[GameTester] {message}");
            }
        }
        
        // Public methods for external testing
        public void RunFullTest()
        {
            StartCoroutine(RunAllTests());
        }
        
        public void TestSpecificSystem(string systemName)
        {
            switch (systemName.ToLower())
            {
                case "inventory":
                    TestInventorySystem();
                    break;
                case "orders":
                    TestOrderSystem();
                    break;
                case "recipes":
                    TestRecipeSystem();
                    break;
                case "equipment":
                    TestEquipmentSystem();
                    break;
                case "player":
                    TestPlayerManager();
                    break;
                default:
                    DebugLog($"Unknown system: {systemName}");
                    break;
            }
        }
        
        // Helper method to create test scenario
        [ContextMenu("Create Test Scenario")]
        public void CreateTestScenario()
        {
            DebugLog("Creating test scenario...");
            
            // Add some test ingredients
            TestInventorySystem();
            
            // Add some money
            AddTestMoney();
            
            // Level up player
            TestLevelUp();
            
            // Start orders
            TestOrderSystem();
            
            DebugLog("Test scenario created!");
        }
    }
}
