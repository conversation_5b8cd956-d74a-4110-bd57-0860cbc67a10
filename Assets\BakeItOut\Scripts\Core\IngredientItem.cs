using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;

namespace BakeItOut.Core
{
    /// <summary>
    /// Component for ingredient items that can be picked up and used in recipes
    /// </summary>
    public class IngredientItem : InteractableBase
    {
        [Header("Ingredient Data")]
        public IngredientData ingredientData;
        public int quantity = 1;
        public Data.QualityLevel quality = Data.QualityLevel.Common;
        
        [Header("Visual")]
        public Renderer itemRenderer;
        public ParticleSystem qualityParticles;
        
        private bool isInitialized = false;
        
        private void Start()
        {
            if (itemRenderer == null)
                itemRenderer = GetComponent<Renderer>();

            if (!isInitialized && ingredientData != null)
            {
                Initialize(ingredientData, quantity, quality);
            }
        }
        
        public void Initialize(IngredientData data, int qty = 1, Data.QualityLevel qual = Data.QualityLevel.Common)
        {
            ingredientData = data;
            quantity = qty;
            quality = qual;
            isInitialized = true;
            
            // Set interaction prompt
            if (ingredientData != null)
            {
                string quantityText = quantity > 1 ? $" x{quantity}" : "";
                interactionPrompt = $"Pick up {ingredientData.ingredientName}{quantityText}";
            }
            
            // Apply visual quality
            ApplyQualityVisuals();
        }
        
        private void ApplyQualityVisuals()
        {
            if (!isInitialized || ingredientData == null) return;
            
            // Apply quality-based visual effects
            if (qualityParticles != null && quality >= Data.QualityLevel.Good)
            {
                qualityParticles.Play();
                
                // Adjust particle intensity based on quality
                var main = qualityParticles.main;
                main.startLifetime = (float)quality * 0.3f;
            }
            
            // Adjust material color based on quality
            if (itemRenderer != null)
            {
                Color qualityColor = GetQualityColor(quality);
                if (itemRenderer.material.HasProperty("_Color"))
                {
                    itemRenderer.material.color = qualityColor;
                }
            }
        }
        
        private Color GetQualityColor(Data.QualityLevel quality)
        {
            switch (quality)
            {
                case Data.QualityLevel.Poor:
                    return Color.gray;
                case Data.QualityLevel.Common:
                    return Color.white;
                case Data.QualityLevel.Good:
                    return Color.green;
                case Data.QualityLevel.Excellent:
                    return Color.blue;
                case Data.QualityLevel.Perfect:
                    return Color.yellow;
                default:
                    return Color.white;
            }
        }
        
        public override bool CanInteract(GameObject player)
        {
            if (!base.CanInteract(player) || ingredientData == null)
                return false;
            
            // Check if player has inventory space
            var inventoryManager = GameManager.Instance?.InventoryManager;
            if (inventoryManager != null)
            {
                return inventoryManager.GetAvailableSlots() > 0 || 
                       inventoryManager.HasIngredient(ingredientData); // Can stack
            }
            
            return true;
        }
        
        public override void Interact(GameObject player)
        {
            if (!CanInteract(player))
                return;
            
            // Add to player's inventory
            PickUp(player);
        }
        
        private void PickUp(GameObject player)
        {
            var inventoryManager = GameManager.Instance?.InventoryManager;
            if (inventoryManager == null) return;
            
            // Try to add to inventory
            if (inventoryManager.AddIngredient(ingredientData, quantity, quality))
            {
                Debug.Log($"Picked up {quantity} {ingredientData.ingredientName} (Quality: {quality})");
                
                // Stop particles
                if (qualityParticles != null)
                {
                    qualityParticles.Stop();
                }
                
                // Remove from scene
                Destroy(gameObject);
            }
            else
            {
                Debug.Log("Inventory is full!");
                // Could show UI message here
            }
        }
        
        public override string GetInteractionPrompt()
        {
            if (ingredientData == null)
                return "Unknown item";
            
            var inventoryManager = GameManager.Instance?.InventoryManager;
            if (inventoryManager != null && inventoryManager.GetAvailableSlots() <= 0 && 
                !inventoryManager.HasIngredient(ingredientData))
            {
                return "Inventory full";
            }
            
            string quantityText = quantity > 1 ? $" x{quantity}" : "";
            return $"Pick up {ingredientData.ingredientName}{quantityText}";
        }
        
        // Public getters
        public IngredientData GetIngredientData() => ingredientData;
        public int GetQuantity() => quantity;
        public Data.QualityLevel GetQuality() => quality;
        
        // Method to split stack
        public IngredientItem SplitStack(int splitQuantity)
        {
            if (splitQuantity >= quantity || splitQuantity <= 0)
                return null;
            
            // Create new ingredient item with split quantity
            GameObject newItem = Instantiate(gameObject, transform.position + Vector3.right * 0.3f, transform.rotation);
            IngredientItem newIngredientItem = newItem.GetComponent<IngredientItem>();
            
            if (newIngredientItem != null)
            {
                newIngredientItem.Initialize(ingredientData, splitQuantity, quality);
                quantity -= splitQuantity;
                
                // Update this item's prompt
                string quantityText = quantity > 1 ? $" x{quantity}" : "";
                interactionPrompt = $"Pick up {ingredientData.ingredientName}{quantityText}";
                
                return newIngredientItem;
            }
            
            return null;
        }
        
        // Method to combine with another ingredient item
        public bool CombineWith(IngredientItem other)
        {
            if (other == null || other.ingredientData != ingredientData)
                return false;
            
            // Check stack limit
            int totalQuantity = quantity + other.quantity;
            if (totalQuantity > ingredientData.stackSize)
                return false;
            
            // Combine quantities and use better quality
            quantity = totalQuantity;
            if (other.quality > quality)
                quality = other.quality;
            
            // Update prompt
            string quantityText = quantity > 1 ? $" x{quantity}" : "";
            interactionPrompt = $"Pick up {ingredientData.ingredientName}{quantityText}";
            
            // Apply new quality visuals
            ApplyQualityVisuals();
            
            // Destroy the other item
            Destroy(other.gameObject);
            
            return true;
        }
    }
}
