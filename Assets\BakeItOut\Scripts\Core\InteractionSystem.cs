using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;
using StarterAssets;

namespace BakeItOut.Core
{
    public class InteractionSystem : MonoBehaviour
    {
        [Header("Interaction Settings")]
        public float interactionRange = 3f;
        public LayerMask interactionLayers = -1;
        public Transform interactionPoint; // Usually the camera transform
        
        [Header("UI")]
        public GameObject interactionPrompt;
        public TMPro.TextMeshProUGUI promptText;
        
        // Current interaction target
        private IInteractable currentInteractable;
        private StarterAssetsInputs input;
        private Camera playerCamera;
        
        // Events
        public System.Action<IInteractable> OnInteractionTargetChanged;
        public System.Action<IInteractable> OnInteractionPerformed;
        
        private void Start()
        {
            // Get required components
            input = GetComponent<StarterAssetsInputs>();
            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindFirstObjectByType<Camera>();
            
            if (interactionPoint == null)
                interactionPoint = playerCamera.transform;
            
            // Initialize UI
            if (interactionPrompt)
                interactionPrompt.SetActive(false);
        }
        
        private void Update()
        {
            DetectInteractables();
            HandleInteractionInput();
        }
        
        private void DetectInteractables()
        {
            IInteractable newTarget = null;
            
            // Raycast from interaction point
            Ray ray = new Ray(interactionPoint.position, interactionPoint.forward);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit, interactionRange, interactionLayers))
            {
                // Check if hit object has an interactable component
                newTarget = hit.collider.GetComponent<IInteractable>();
                
                // If no direct component, check parent objects
                if (newTarget == null)
                    newTarget = hit.collider.GetComponentInParent<IInteractable>();
            }
            
            // Update current target if changed
            if (newTarget != currentInteractable)
            {
                // Exit previous target
                if (currentInteractable != null)
                {
                    currentInteractable.OnInteractionExit();
                }
                
                // Enter new target
                currentInteractable = newTarget;
                if (currentInteractable != null)
                {
                    currentInteractable.OnInteractionEnter();
                }
                
                UpdateInteractionUI();
                OnInteractionTargetChanged?.Invoke(currentInteractable);
            }
        }
        
        private void HandleInteractionInput()
        {
            // Check for interaction input from our custom input system
            if (currentInteractable != null && CanInteract())
            {
                // Check for interact button press (we'll need to add this to StarterAssetsInputs)
                if (Input.GetKeyDown(KeyCode.E)) // Fallback to direct input for now
                {
                    PerformInteraction();
                }
            }
        }
        
        private bool CanInteract()
        {
            if (currentInteractable == null)
                return false;
            
            return currentInteractable.CanInteract(gameObject);
        }
        
        private void PerformInteraction()
        {
            if (currentInteractable != null && CanInteract())
            {
                currentInteractable.Interact(gameObject);
                OnInteractionPerformed?.Invoke(currentInteractable);
            }
        }
        
        private void UpdateInteractionUI()
        {
            if (interactionPrompt == null)
                return;
            
            if (currentInteractable != null && CanInteract())
            {
                interactionPrompt.SetActive(true);
                if (promptText != null)
                {
                    promptText.text = currentInteractable.GetInteractionPrompt();
                }
            }
            else
            {
                interactionPrompt.SetActive(false);
            }
        }
        
        // Public methods for external access
        public IInteractable GetCurrentTarget()
        {
            return currentInteractable;
        }
        
        public bool HasInteractionTarget()
        {
            return currentInteractable != null;
        }
        
        public void ForceInteraction()
        {
            PerformInteraction();
        }
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            if (interactionPoint != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(interactionPoint.position, interactionPoint.forward * interactionRange);
                
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(interactionPoint.position + interactionPoint.forward * interactionRange, 0.1f);
            }
        }
    }
    
    // Interface for all interactable objects
    public interface IInteractable
    {
        string GetInteractionPrompt();
        bool CanInteract(GameObject player);
        void Interact(GameObject player);
        void OnInteractionEnter();
        void OnInteractionExit();
    }
    
    // Base class for interactable objects
    public abstract class InteractableBase : MonoBehaviour, IInteractable
    {
        [Header("Interaction")]
        public string interactionPrompt = "Press E to interact";
        public bool isInteractable = true;
        
        protected bool isPlayerNearby = false;
        
        public virtual string GetInteractionPrompt()
        {
            return interactionPrompt;
        }
        
        public virtual bool CanInteract(GameObject player)
        {
            return isInteractable && isPlayerNearby;
        }
        
        public abstract void Interact(GameObject player);
        
        public virtual void OnInteractionEnter()
        {
            isPlayerNearby = true;
        }
        
        public virtual void OnInteractionExit()
        {
            isPlayerNearby = false;
        }
    }
}
