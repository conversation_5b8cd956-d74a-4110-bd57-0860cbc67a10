using UnityEngine;
using BakeItOut.Data;

namespace BakeItOut.Core
{
    /// <summary>
    /// Utility class to create sample data for testing the bakery game
    /// </summary>
    public class SampleDataCreator : MonoBehaviour
    {
        [Header("Create Sample Data")]
        public bool createSampleData = false;
        
        [ContextMenu("Create Sample Ingredients")]
        public void CreateSampleIngredients()
        {
            CreateIngredient("Flour", IngredientType.Flour, 2f, 50, false, 0f, Data.QualityLevel.Common);
            CreateIngredient("Sugar", IngredientType.Sugar, 3f, 30, false, 0f, Data.QualityLevel.Common);
            CreateIngredient("Eggs", IngredientType.Eggs, 5f, 12, true, 600f, Data.QualityLevel.Common);
            CreateIngredient("Butter", IngredientType.Dairy, 4f, 20, true, 1200f, Data.QualityLevel.Common);
            CreateIngredient("Vanilla Extract", IngredientType.Spices, 8f, 10, false, 0f, Data.QualityLevel.Good);
            CreateIngredient("Chocolate Chips", IngredientType.Chocolate, 6f, 25, false, 0f, Data.QualityLevel.Common);
            CreateIngredient("Strawberries", IngredientType.Fruits, 7f, 15, true, 300f, Data.QualityLevel.Good);
            CreateIngredient("Almonds", IngredientType.Nuts, 9f, 20, false, 0f, Data.QualityLevel.Good);
        }
        
        [ContextMenu("Create Sample Recipes")]
        public void CreateSampleRecipes()
        {
            CreateBasicBreadRecipe();
            CreateChocolateChipCookieRecipe();
            CreateStrawberryCakeRecipe();
        }
        
        [ContextMenu("Create Sample Equipment")]
        public void CreateSampleEquipment()
        {
            CreateEquipment("Basic Oven", EquipmentType.Oven, 5, 1.0f, 1, true, 3, 500f, 0.8f);
            CreateEquipment("Mixing Bowl", EquipmentType.Mixer, 3, 1.2f, 1, false, 0, 0f, 0f);
            CreateEquipment("Prep Counter", EquipmentType.PrepStation, 3, 1.5f, 2, false, 0, 0f, 0f);
        }
        
        private void CreateIngredient(string name, IngredientType type, float price, int stackSize,
                                    bool perishable, float spoilTime, Data.QualityLevel quality)
        {
            IngredientData ingredient = ScriptableObject.CreateInstance<IngredientData>();
            ingredient.ingredientName = name;
            ingredient.type = type;
            ingredient.basePrice = price;
            ingredient.stackSize = stackSize;
            ingredient.isPerishable = perishable;
            ingredient.spoilTime = spoilTime;
            ingredient.baseQuality = quality;

            #if UNITY_EDITOR
            string path = $"Assets/BakeItOut/ScriptableObjects/Ingredients/{name}.asset";
            UnityEditor.AssetDatabase.CreateAsset(ingredient, path);
            #endif
            Debug.Log($"Created ingredient: {name}");
        }
        
        private void CreateBasicBreadRecipe()
        {
            RecipeData recipe = ScriptableObject.CreateInstance<RecipeData>();
            recipe.recipeName = "Basic Bread";
            recipe.description = "A simple, delicious bread perfect for any meal.";
            recipe.requiredEquipment = EquipmentType.Oven;
            recipe.preparationTime = 30f;
            recipe.cookingTime = 45f;
            recipe.baseExperience = 15;
            recipe.basePrice = 8f;
            recipe.isUnlockedByDefault = true;
            
            // Note: In a real implementation, you'd load the actual ingredient assets
            recipe.requiredIngredients = new RecipeIngredient[]
            {
                new RecipeIngredient { quantity = 3 }, // Flour
                new RecipeIngredient { quantity = 1 }, // Sugar
                new RecipeIngredient { quantity = 1 }  // Eggs
            };
            
            recipe.steps = new RecipeStep[]
            {
                new RecipeStep { stepDescription = "Mix dry ingredients", requiredEquipment = EquipmentType.Mixer, duration = 10f },
                new RecipeStep { stepDescription = "Add wet ingredients", requiredEquipment = EquipmentType.Mixer, duration = 10f },
                new RecipeStep { stepDescription = "Knead dough", requiredEquipment = EquipmentType.PrepStation, duration = 10f },
                new RecipeStep { stepDescription = "Bake bread", requiredEquipment = EquipmentType.Oven, duration = 45f }
            };
            
            #if UNITY_EDITOR
            string path = "Assets/BakeItOut/ScriptableObjects/Recipes/BasicBread.asset";
            UnityEditor.AssetDatabase.CreateAsset(recipe, path);
            #endif
            Debug.Log("Created recipe: Basic Bread");
        }
        
        private void CreateChocolateChipCookieRecipe()
        {
            RecipeData recipe = ScriptableObject.CreateInstance<RecipeData>();
            recipe.recipeName = "Chocolate Chip Cookies";
            recipe.description = "Classic chocolate chip cookies that everyone loves.";
            recipe.requiredEquipment = EquipmentType.Oven;
            recipe.preparationTime = 20f;
            recipe.cookingTime = 15f;
            recipe.baseExperience = 10;
            recipe.basePrice = 12f;
            recipe.isUnlockedByDefault = true;
            
            recipe.requiredIngredients = new RecipeIngredient[]
            {
                new RecipeIngredient { quantity = 2 }, // Flour
                new RecipeIngredient { quantity = 1 }, // Sugar
                new RecipeIngredient { quantity = 1 }, // Butter
                new RecipeIngredient { quantity = 1 }, // Eggs
                new RecipeIngredient { quantity = 1 }  // Chocolate Chips
            };
            
            recipe.steps = new RecipeStep[]
            {
                new RecipeStep { stepDescription = "Cream butter and sugar", requiredEquipment = EquipmentType.Mixer, duration = 5f },
                new RecipeStep { stepDescription = "Add eggs", requiredEquipment = EquipmentType.Mixer, duration = 3f },
                new RecipeStep { stepDescription = "Mix in flour", requiredEquipment = EquipmentType.Mixer, duration = 5f },
                new RecipeStep { stepDescription = "Fold in chocolate chips", requiredEquipment = EquipmentType.PrepStation, duration = 2f },
                new RecipeStep { stepDescription = "Bake cookies", requiredEquipment = EquipmentType.Oven, duration = 15f }
            };
            
            #if UNITY_EDITOR
            string path = "Assets/BakeItOut/ScriptableObjects/Recipes/ChocolateChipCookies.asset";
            UnityEditor.AssetDatabase.CreateAsset(recipe, path);
            #endif
            Debug.Log("Created recipe: Chocolate Chip Cookies");
        }
        
        private void CreateStrawberryCakeRecipe()
        {
            RecipeData recipe = ScriptableObject.CreateInstance<RecipeData>();
            recipe.recipeName = "Strawberry Cake";
            recipe.description = "A delightful cake with fresh strawberries.";
            recipe.requiredEquipment = EquipmentType.Oven;
            recipe.preparationTime = 45f;
            recipe.cookingTime = 30f;
            recipe.baseExperience = 25;
            recipe.basePrice = 20f;
            recipe.requiredPlayerLevel = 3;
            recipe.isUnlockedByDefault = false;
            recipe.unlockLevel = 3;
            
            recipe.requiredIngredients = new RecipeIngredient[]
            {
                new RecipeIngredient { quantity = 3 }, // Flour
                new RecipeIngredient { quantity = 2 }, // Sugar
                new RecipeIngredient { quantity = 2 }, // Butter
                new RecipeIngredient { quantity = 2 }, // Eggs
                new RecipeIngredient { quantity = 2 }, // Strawberries
                new RecipeIngredient { quantity = 1 }  // Vanilla Extract
            };
            
            recipe.steps = new RecipeStep[]
            {
                new RecipeStep { stepDescription = "Prepare strawberries", requiredEquipment = EquipmentType.PrepStation, duration = 10f },
                new RecipeStep { stepDescription = "Cream butter and sugar", requiredEquipment = EquipmentType.Mixer, duration = 8f },
                new RecipeStep { stepDescription = "Add eggs and vanilla", requiredEquipment = EquipmentType.Mixer, duration = 5f },
                new RecipeStep { stepDescription = "Mix in flour", requiredEquipment = EquipmentType.Mixer, duration = 7f },
                new RecipeStep { stepDescription = "Fold in strawberries", requiredEquipment = EquipmentType.PrepStation, duration = 5f },
                new RecipeStep { stepDescription = "Bake cake", requiredEquipment = EquipmentType.Oven, duration = 30f }
            };
            
            #if UNITY_EDITOR
            string path = "Assets/BakeItOut/ScriptableObjects/Recipes/StrawberryCake.asset";
            UnityEditor.AssetDatabase.CreateAsset(recipe, path);
            #endif
            Debug.Log("Created recipe: Strawberry Cake");
        }
        
        private void CreateEquipment(string name, EquipmentType type, int maxLevel, float baseSpeed, 
                                   int maxItems, bool canAutomate, int autoLevel, float autoCost, float autoEfficiency)
        {
            EquipmentData equipment = ScriptableObject.CreateInstance<EquipmentData>();
            equipment.equipmentName = name;
            equipment.equipmentType = type;
            equipment.maxLevel = maxLevel;
            equipment.baseProcessingSpeed = baseSpeed;
            equipment.maxSimultaneousItems = maxItems;
            equipment.canBeAutomated = canAutomate;
            equipment.automationUnlockLevel = autoLevel;
            equipment.automationCost = autoCost;
            equipment.automationEfficiency = autoEfficiency;
            
            // Create upgrade costs
            equipment.upgradeCosts = new UpgradeCost[maxLevel - 1];
            for (int i = 0; i < equipment.upgradeCosts.Length; i++)
            {
                equipment.upgradeCosts[i] = new UpgradeCost
                {
                    level = i + 2,
                    cost = 100f * Mathf.Pow(1.5f, i)
                };
            }
            
            #if UNITY_EDITOR
            string path = $"Assets/BakeItOut/ScriptableObjects/Equipment/{name.Replace(" ", "")}.asset";
            UnityEditor.AssetDatabase.CreateAsset(equipment, path);
            #endif
            Debug.Log($"Created equipment: {name}");
        }
        
        private void Update()
        {
            if (createSampleData)
            {
                createSampleData = false;
                CreateSampleIngredients();
                CreateSampleRecipes();
                CreateSampleEquipment();
                #if UNITY_EDITOR
                UnityEditor.AssetDatabase.SaveAssets();
                UnityEditor.AssetDatabase.Refresh();
                #endif
            }
        }
    }
}
