using UnityEngine;
using BakeItOut.Core;
using BakeItOut.Equipment;
using BakeItOut.Data;
using BakeItOut.UI;
using StarterAssets;

namespace BakeItOut.Core
{
    /// <summary>
    /// Utility script to set up a test scene for the bakery game
    /// </summary>
    public class SceneSetup : MonoBehaviour
    {
        [Header("Scene Setup")]
        public bool setupOnStart = true;
        public bool createSampleData = true;
        
        [Header("Player Setup")]
        public GameObject playerPrefab;
        public Transform playerSpawnPoint;
        
        [Header("Equipment Positions")]
        public Transform[] ovenPositions;
        public Transform[] mixerPositions;
        public Transform[] prepStationPositions;
        
        [Header("Environment")]
        public GameObject floorPrefab;
        public GameObject wallPrefab;
        public Vector3 roomSize = new Vector3(20f, 3f, 15f);
        
        [Header("Lighting")]
        public Light mainLight;
        public Color ambientColor = Color.gray;
        public float ambientIntensity = 0.3f;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupScene();
            }
        }
        
        [ContextMenu("Setup Scene")]
        public void SetupScene()
        {
            Debug.Log("Setting up Bake It Out test scene...");
            
            // Create sample data first
            if (createSampleData)
            {
                CreateSampleData();
            }
            
            // Setup environment
            SetupEnvironment();
            
            // Setup lighting
            SetupLighting();
            
            // Setup player
            SetupPlayer();
            
            // Setup equipment
            SetupEquipment();
            
            // Setup game managers
            SetupGameManagers();
            
            // Setup UI
            SetupUI();
            
            Debug.Log("Scene setup complete!");
        }
        
        private void CreateSampleData()
        {
            var sampleCreator = FindFirstObjectByType<SampleDataCreator>();
            if (sampleCreator == null)
            {
                GameObject creatorObj = new GameObject("SampleDataCreator");
                sampleCreator = creatorObj.AddComponent<SampleDataCreator>();
            }
            
            sampleCreator.CreateSampleIngredients();
            sampleCreator.CreateSampleRecipes();
            sampleCreator.CreateSampleEquipment();
        }
        
        private void SetupEnvironment()
        {
            // Create floor - always create it, not dependent on floorPrefab
            GameObject floor = CreatePrimitive(PrimitiveType.Plane);
            floor.name = "Floor";
            floor.transform.localScale = new Vector3(roomSize.x / 10f, 1f, roomSize.z / 10f);
            floor.transform.position = Vector3.zero;

            // Add a simple material
            var renderer = floor.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Create a new material to avoid modifying the default material
                Material floorMaterial = new Material(Shader.Find("Standard"));
                floorMaterial.color = new Color(0.8f, 0.7f, 0.6f); // Light brown
                floorMaterial.SetFloat("_Metallic", 0f);
                floorMaterial.SetFloat("_Glossiness", 0.2f);
                renderer.material = floorMaterial;
            }

            // Create walls
            CreateWalls();
        }
        
        private void CreateWalls()
        {
            float halfWidth = roomSize.x / 2f;
            float halfDepth = roomSize.z / 2f;
            float height = roomSize.y;
            
            // Front wall
            CreateWall(new Vector3(0, height / 2f, halfDepth), new Vector3(roomSize.x, height, 0.2f));
            
            // Back wall
            CreateWall(new Vector3(0, height / 2f, -halfDepth), new Vector3(roomSize.x, height, 0.2f));
            
            // Left wall
            CreateWall(new Vector3(-halfWidth, height / 2f, 0), new Vector3(0.2f, height, roomSize.z));
            
            // Right wall
            CreateWall(new Vector3(halfWidth, height / 2f, 0), new Vector3(0.2f, height, roomSize.z));
        }
        
        private void CreateWall(Vector3 position, Vector3 scale)
        {
            GameObject wall = CreatePrimitive(PrimitiveType.Cube);
            wall.name = "Wall";
            wall.transform.position = position;
            wall.transform.localScale = scale;

            var renderer = wall.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Create a new material for walls
                Material wallMaterial = new Material(Shader.Find("Standard"));
                wallMaterial.color = new Color(0.9f, 0.9f, 0.8f); // Off-white
                wallMaterial.SetFloat("_Metallic", 0f);
                wallMaterial.SetFloat("_Glossiness", 0.1f);
                renderer.material = wallMaterial;
            }
        }
        
        private void SetupLighting()
        {
            // Setup main directional light
            if (mainLight == null)
            {
                GameObject lightObj = new GameObject("Main Light");
                mainLight = lightObj.AddComponent<Light>();
            }
            
            mainLight.type = LightType.Directional;
            mainLight.color = Color.white;
            mainLight.intensity = 1.2f;
            mainLight.transform.rotation = Quaternion.Euler(45f, 45f, 0f);
            
            // Setup ambient lighting
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
            RenderSettings.ambientLight = ambientColor * ambientIntensity;
            
            // Add some point lights for atmosphere
            CreatePointLight(new Vector3(-5f, 2f, -3f), Color.yellow, 2f, 8f);
            CreatePointLight(new Vector3(5f, 2f, 3f), Color.yellow, 2f, 8f);
        }
        
        private void CreatePointLight(Vector3 position, Color color, float intensity, float range)
        {
            GameObject lightObj = new GameObject("Point Light");
            lightObj.transform.position = position;
            
            Light pointLight = lightObj.AddComponent<Light>();
            pointLight.type = LightType.Point;
            pointLight.color = color;
            pointLight.intensity = intensity;
            pointLight.range = range;
        }
        
        private void SetupPlayer()
        {
            // Find existing player or create new one
            var existingPlayer = FindFirstObjectByType<FirstPersonController>();
            if (existingPlayer != null)
            {
                // Add bakery controller to existing player
                var bakeryController = existingPlayer.GetComponent<BakeryPlayerController>();
                if (bakeryController == null)
                {
                    bakeryController = existingPlayer.gameObject.AddComponent<BakeryPlayerController>();
                }
                
                var interactionSystem = existingPlayer.GetComponent<InteractionSystem>();
                if (interactionSystem == null)
                {
                    interactionSystem = existingPlayer.gameObject.AddComponent<InteractionSystem>();
                }
                
                // Position player at spawn point
                if (playerSpawnPoint != null)
                {
                    existingPlayer.transform.position = playerSpawnPoint.position;
                    existingPlayer.transform.rotation = playerSpawnPoint.rotation;
                }
                else
                {
                    existingPlayer.transform.position = new Vector3(0f, 1f, -5f);
                }
            }
            else if (playerPrefab != null)
            {
                // Create new player from prefab
                Vector3 spawnPos = playerSpawnPoint != null ? playerSpawnPoint.position : new Vector3(0f, 1f, -5f);
                Quaternion spawnRot = playerSpawnPoint != null ? playerSpawnPoint.rotation : Quaternion.identity;
                
                GameObject player = Instantiate(playerPrefab, spawnPos, spawnRot);
                
                // Add required components
                var bakeryController = player.GetComponent<BakeryPlayerController>();
                if (bakeryController == null)
                {
                    bakeryController = player.AddComponent<BakeryPlayerController>();
                }
                
                var interactionSystem = player.GetComponent<InteractionSystem>();
                if (interactionSystem == null)
                {
                    interactionSystem = player.AddComponent<InteractionSystem>();
                }
            }
        }
        
        private void SetupEquipment()
        {
            // Create ovens
            if (ovenPositions != null)
            {
                for (int i = 0; i < ovenPositions.Length; i++)
                {
                    CreateEquipment<Oven>("Oven_" + i, ovenPositions[i], PrimitiveType.Cube, new Vector3(2f, 1f, 1.5f), Color.gray);
                }
            }
            else
            {
                // Default oven positions
                CreateEquipment<Oven>("Oven_1", new Vector3(-6f, 0.5f, 2f), PrimitiveType.Cube, new Vector3(2f, 1f, 1.5f), Color.gray);
                CreateEquipment<Oven>("Oven_2", new Vector3(-6f, 0.5f, -2f), PrimitiveType.Cube, new Vector3(2f, 1f, 1.5f), Color.gray);
            }
            
            // Create mixing stations
            if (mixerPositions != null)
            {
                for (int i = 0; i < mixerPositions.Length; i++)
                {
                    CreateEquipment<MixingStation>("Mixer_" + i, mixerPositions[i], PrimitiveType.Cylinder, new Vector3(1.5f, 0.8f, 1.5f), Color.white);
                }
            }
            else
            {
                CreateEquipment<MixingStation>("Mixer_1", new Vector3(0f, 0.4f, 2f), PrimitiveType.Cylinder, new Vector3(1.5f, 0.8f, 1.5f), Color.white);
            }
            
            // Create prep stations
            if (prepStationPositions != null)
            {
                for (int i = 0; i < prepStationPositions.Length; i++)
                {
                    CreateEquipment<PrepStation>("PrepStation_" + i, prepStationPositions[i], PrimitiveType.Cube, new Vector3(2f, 0.8f, 1f), new Color(0.8f, 0.6f, 0.4f));
                }
            }
            else
            {
                CreateEquipment<PrepStation>("PrepStation_1", new Vector3(6f, 0.4f, 0f), PrimitiveType.Cube, new Vector3(2f, 0.8f, 1f), new Color(0.8f, 0.6f, 0.4f));
            }
        }
        
        private void CreateEquipment<T>(string name, Vector3 position, PrimitiveType primitiveType, Vector3 scale, Color color) where T : BaseEquipment
        {
            GameObject equipmentObj = CreatePrimitive(primitiveType);
            equipmentObj.name = name;
            equipmentObj.transform.position = position;
            equipmentObj.transform.localScale = scale;
            
            // Set color
            var renderer = equipmentObj.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = color;
            }
            
            // Add equipment component
            T equipment = equipmentObj.AddComponent<T>();
            
            // Create sample equipment data if needed
            // This would normally be assigned from ScriptableObject assets
        }
        
        private void CreateEquipment<T>(string name, Transform transform, PrimitiveType primitiveType, Vector3 scale, Color color) where T : BaseEquipment
        {
            CreateEquipment<T>(name, transform.position, primitiveType, scale, color);
        }
        
        private void SetupGameManagers()
        {
            // Create GameManager if it doesn't exist
            var gameManager = FindFirstObjectByType<GameManager>();
            if (gameManager == null)
            {
                GameObject managerObj = new GameObject("GameManager");
                gameManager = managerObj.AddComponent<GameManager>();
            }
        }
        
        private void SetupUI()
        {
            // Find or create UI Manager
            var uiManager = FindFirstObjectByType<UIManager>();
            if (uiManager == null)
            {
                Debug.Log("UIManager not found. Please create UI Canvas with UIManager component.");
            }
        }
        
        private GameObject CreatePrimitive(PrimitiveType type)
        {
            GameObject primitive = GameObject.CreatePrimitive(type);
            
            // Ensure it has a collider for interactions
            var collider = primitive.GetComponent<Collider>();
            if (collider == null)
            {
                primitive.AddComponent<BoxCollider>();
            }
            
            return primitive;
        }
    }
}
