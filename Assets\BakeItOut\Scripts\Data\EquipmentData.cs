using UnityEngine;

namespace BakeItOut.Data
{
    [CreateAssetMenu(fileName = "New Equipment", menuName = "Bake It Out/Equipment")]
    public class EquipmentData : ScriptableObject
    {
        [Header("Basic Info")]
        public string equipmentName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        public GameObject prefab;
        
        [Header("Equipment Properties")]
        public EquipmentType equipmentType;
        public int maxLevel = 5;
        public float baseProcessingSpeed = 1.0f;
        public int maxSimultaneousItems = 1;
        
        [Header("Automation")]
        public bool canBeAutomated = false;
        public int automationUnlockLevel = 3;
        public float automationCost = 100f;
        public float automationEfficiency = 0.8f; // 80% efficiency when automated
        
        [Header("Upgrade Costs")]
        public UpgradeCost[] upgradeCosts;
        
        [Header("Power Requirements")]
        public bool requiresPower = false;
        public float powerConsumption = 1.0f;
        
        public float GetProcessingSpeedAtLevel(int level)
        {
            return baseProcessingSpeed * (1 + (level - 1) * 0.2f); // 20% increase per level
        }
        
        public int GetCapacityAtLevel(int level)
        {
            return maxSimultaneousItems + (level - 1); // +1 capacity per level
        }
        
        public float GetUpgradeCost(int currentLevel)
        {
            if (currentLevel >= maxLevel || currentLevel < 1) return -1;
            
            int upgradeIndex = currentLevel - 1;
            if (upgradeIndex < upgradeCosts.Length)
            {
                return upgradeCosts[upgradeIndex].cost;
            }
            
            // Default exponential cost if not specified
            return 50f * Mathf.Pow(2f, currentLevel - 1);
        }
    }
    
    [System.Serializable]
    public class UpgradeCost
    {
        public int level;
        public float cost;
        public IngredientData[] requiredMaterials;
        public int[] materialQuantities;
    }
}
