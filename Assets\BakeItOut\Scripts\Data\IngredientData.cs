using UnityEngine;

namespace BakeItOut.Data
{
    [CreateAssetMenu(fileName = "New Ingredient", menuName = "Bake It Out/Ingredient")]
    public class IngredientData : ScriptableObject
    {
        [Header("Basic Info")]
        public string ingredientName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        public GameObject prefab;
        
        [Header("Properties")]
        public IngredientType type;
        public float basePrice = 1.0f;
        public int stackSize = 10;
        public bool isPerishable = false;
        public float spoilTime = 300f; // seconds
        
        [Header("Quality")]
        public QualityLevel baseQuality = QualityLevel.Common;
        public float qualityMultiplier = 1.0f;
        
        [Header("Processing")]
        public bool canBeProcessed = false;
        public IngredientData[] processedResults;
        public float processingTime = 30f;
    }
    
    public enum IngredientType
    {
        Flour,
        Dairy,
        Eggs,
        Sugar,
        Spices,
        Fruits,
        Nuts,
        Chocolate,
        Yeast,
        Oil,
        Decoration,
        Other
    }
    
    public enum QualityLevel
    {
        Poor = 1,
        Common = 2,
        Good = 3,
        Excellent = 4,
        Perfect = 5
    }
}
