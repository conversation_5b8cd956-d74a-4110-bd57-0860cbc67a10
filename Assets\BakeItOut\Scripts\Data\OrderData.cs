using UnityEngine;
using System.Collections.Generic;

namespace BakeItOut.Data
{
    [CreateAssetMenu(fileName = "New Order", menuName = "Bake It Out/Order")]
    public class OrderData : ScriptableObject
    {
        [Header("Basic Info")]
        public string customerName;
        public Sprite customerAvatar;
        [TextArea(2, 4)]
        public string orderDescription;
        
        [Header("Order Requirements")]
        public OrderItem[] requiredItems;
        public float timeLimit = 300f; // 5 minutes default
        public QualityLevel minimumQuality = QualityLevel.Common;
        
        [Header("Rewards")]
        public float basePayment = 10f;
        public int baseExperience = 5;
        public float tipMultiplier = 1.0f;
        public float qualityBonus = 0.2f; // 20% bonus per quality level above minimum
        
        [Header("Difficulty")]
        public OrderDifficulty difficulty = OrderDifficulty.Easy;
        public int minimumPlayerLevel = 1;
        
        [Header("Special Conditions")]
        public bool isRushOrder = false;
        public bool requiresPerfectQuality = false;
        public bool allowsSubstitutions = true;
        
        public float CalculateReward(List<OrderItem> completedItems, float completionTime, bool isMultiplayer = false)
        {
            float reward = basePayment;
            
            // Time bonus (faster completion = higher reward)
            float timeRatio = Mathf.Clamp01((timeLimit - completionTime) / timeLimit);
            reward += basePayment * tipMultiplier * timeRatio;
            
            // Quality bonus
            float avgQuality = 0f;
            foreach (var item in completedItems)
            {
                avgQuality += (float)item.quality;
            }
            avgQuality /= completedItems.Count;
            
            float qualityMultiplier = 1f + ((avgQuality - (float)minimumQuality) * qualityBonus);
            reward *= qualityMultiplier;
            
            // Rush order bonus
            if (isRushOrder)
                reward *= 1.5f;
            
            // Multiplayer bonus (shared reward)
            if (isMultiplayer)
                reward *= 1.2f;
            
            return reward;
        }
        
        public int CalculateExperience(List<OrderItem> completedItems, bool isMultiplayer = false)
        {
            int experience = baseExperience;
            
            // Difficulty bonus
            experience += (int)difficulty * 2;
            
            // Quality bonus
            foreach (var item in completedItems)
            {
                if (item.quality > minimumQuality)
                    experience += ((int)item.quality - (int)minimumQuality);
            }
            
            // Multiplayer bonus
            if (isMultiplayer)
                experience = Mathf.RoundToInt(experience * 1.1f);
            
            return experience;
        }
    }
    
    [System.Serializable]
    public class OrderItem
    {
        public RecipeData recipe;
        public int quantity = 1;
        public QualityLevel quality = QualityLevel.Common;
        public bool isCompleted = false;
    }
    
    public enum OrderDifficulty
    {
        Easy = 1,
        Medium = 2,
        Hard = 3,
        Expert = 4,
        Master = 5
    }
}
