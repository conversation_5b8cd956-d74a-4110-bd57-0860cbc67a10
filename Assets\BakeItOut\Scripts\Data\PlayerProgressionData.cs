using UnityEngine;
using System.Collections.Generic;

namespace BakeItOut.Data
{
    [CreateAssetMenu(fileName = "Player Progression", menuName = "Bake It Out/Player Progression")]
    public class PlayerProgressionData : ScriptableObject
    {
        [Header("Level System")]
        public int maxLevel = 50;
        public AnimationCurve experienceRequiredCurve;
        public LevelReward[] levelRewards;
        
        [Header("Starting Values")]
        public float startingMoney = 100f;
        public IngredientData[] startingIngredients;
        public int[] startingQuantities;
        public RecipeData[] startingRecipes;
        
        public int GetExperienceRequired(int level)
        {
            if (level <= 1) return 0;
            if (level > maxLevel) return int.MaxValue;
            
            float normalizedLevel = (float)(level - 1) / (maxLevel - 1);
            return Mathf.RoundToInt(experienceRequiredCurve.Evaluate(normalizedLevel) * 1000f);
        }
        
        public LevelReward GetRewardForLevel(int level)
        {
            foreach (var reward in levelRewards)
            {
                if (reward.level == level)
                    return reward;
            }
            return null;
        }
        
        public List<LevelReward> GetRewardsInRange(int fromLevel, int toLevel)
        {
            List<LevelReward> rewards = new List<LevelReward>();
            foreach (var reward in levelRewards)
            {
                if (reward.level > fromLevel && reward.level <= toLevel)
                    rewards.Add(reward);
            }
            return rewards;
        }
    }
    
    [System.Serializable]
    public class LevelReward
    {
        public int level;
        public RewardType rewardType;
        public float moneyReward;
        public RecipeData[] unlockedRecipes;
        public EquipmentData[] unlockedEquipment;
        public TechnologyData[] unlockedTechnologies;
        public string rewardDescription;
    }
    
    public enum RewardType
    {
        Money,
        Recipe,
        Equipment,
        Technology,
        Mixed
    }
}
