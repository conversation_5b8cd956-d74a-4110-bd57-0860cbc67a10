using UnityEngine;
using System.Collections.Generic;

namespace BakeItOut.Data
{
    [CreateAssetMenu(fileName = "New Recipe", menuName = "Bake It Out/Recipe")]
    public class RecipeData : ScriptableObject
    {
        [Header("Basic Info")]
        public string recipeName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        public GameObject resultPrefab;
        
        [Header("Requirements")]
        public RecipeIngredient[] requiredIngredients;
        public EquipmentType requiredEquipment;
        public int requiredPlayerLevel = 1;
        
        [Header("Process")]
        public float preparationTime = 30f;
        public float cookingTime = 60f;
        public RecipeStep[] steps;
        
        [Header("Results")]
        public int baseExperience = 10;
        public float basePrice = 5.0f;
        public QualityLevel baseQuality = QualityLevel.Common;
        
        [Header("Unlock")]
        public bool isUnlockedByDefault = true;
        public int unlockLevel = 1;
        public float unlockCost = 0f;
        public RecipeData[] prerequisiteRecipes;
        
        public bool CanPlayerMake(int playerLevel, List<RecipeData> unlockedRecipes)
        {
            if (playerLevel < requiredPlayerLevel) return false;
            if (!isUnlockedByDefault && !unlockedRecipes.Contains(this)) return false;
            
            foreach (var prereq in prerequisiteRecipes)
            {
                if (!unlockedRecipes.Contains(prereq)) return false;
            }
            
            return true;
        }
    }
    
    [System.Serializable]
    public class RecipeIngredient
    {
        public IngredientData ingredient;
        public int quantity;
        public QualityLevel minimumQuality = QualityLevel.Poor;
    }
    
    [System.Serializable]
    public class RecipeStep
    {
        public string stepDescription;
        public EquipmentType requiredEquipment;
        public float duration;
        public bool requiresPlayerInput = false;
    }
    
    public enum EquipmentType
    {
        None,
        PrepStation,
        Mixer,
        Oven,
        Stove,
        Refrigerator,
        DecorationStation,
        PackagingStation
    }
}
