using UnityEngine;

namespace BakeItOut.Data
{
    [CreateAssetMenu(fileName = "New Technology", menuName = "Bake It Out/Technology")]
    public class TechnologyData : ScriptableObject
    {
        [Header("Basic Info")]
        public string technologyName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        
        [Header("Requirements")]
        public int requiredLevel = 1;
        public float researchCost = 100f;
        public TechnologyData[] prerequisiteTechnologies;
        
        [Header("Effects")]
        public TechnologyType technologyType;
        public float effectValue = 1.0f;
        public EquipmentType affectedEquipment = EquipmentType.None;
        
        [Header("Automation")]
        public bool enablesAutomation = false;
        public EquipmentType automationTarget = EquipmentType.None;
        public float automationEfficiency = 0.8f;
        
        [Header("Global Effects")]
        public bool affectsAllEquipment = false;
        public float globalSpeedBonus = 0f;
        public float globalQualityBonus = 0f;
        public float globalCostReduction = 0f;
        
        public bool CanPlayerResearch(int playerLevel, TechnologyData[] researchedTechnologies)
        {
            if (playerLevel < requiredLevel) return false;
            
            foreach (var prereq in prerequisiteTechnologies)
            {
                bool hasPrereq = false;
                foreach (var researched in researchedTechnologies)
                {
                    if (researched == prereq)
                    {
                        hasPrereq = true;
                        break;
                    }
                }
                if (!hasPrereq) return false;
            }
            
            return true;
        }
    }
    
    public enum TechnologyType
    {
        SpeedIncrease,
        QualityImprovement,
        CostReduction,
        CapacityIncrease,
        AutomationUnlock,
        NewRecipeCategory,
        IngredientEfficiency,
        PowerEfficiency,
        CustomerSatisfaction
    }
}
