using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;
using System.Collections.Generic;

namespace BakeItOut.Equipment
{
    /// <summary>
    /// Base class for all bakery equipment
    /// </summary>
    public abstract class BaseEquipment : InteractableBase
    {
        [Header("Equipment Data")]
        public EquipmentData equipmentData;
        
        [Header("Equipment State")]
        public bool isOperational = true;
        public bool isInUse = false;
        public float operationProgress = 0f;
        
        [Header("Visual Feedback")]
        public GameObject operationIndicator;
        public ParticleSystem operationParticles;
        public AudioSource operationAudio;
        
        // Equipment instance data
        protected EquipmentInstance equipmentInstance;
        protected List<ActiveRecipe> currentRecipes = new List<ActiveRecipe>();
        
        // Events
        public System.Action<BaseEquipment> OnEquipmentStarted;
        public System.Action<BaseEquipment> OnEquipmentCompleted;
        public System.Action<BaseEquipment> OnEquipmentStopped;
        
        protected virtual void Start()
        {
            InitializeEquipment();
            RegisterWithManager();
        }
        
        protected virtual void Update()
        {
            if (isOperational && isInUse)
            {
                UpdateOperation();
            }
            
            UpdateVisualFeedback();
        }
        
        protected virtual void InitializeEquipment()
        {
            if (equipmentData == null)
            {
                Debug.LogError($"Equipment {gameObject.name} is missing EquipmentData!");
                return;
            }
            
            // Set default interaction prompt
            interactionPrompt = $"Use {equipmentData.equipmentName}";
            
            // Initialize visual feedback
            if (operationIndicator)
                operationIndicator.SetActive(false);
        }
        
        protected virtual void RegisterWithManager()
        {
            var equipmentManager = GameManager.Instance?.EquipmentManager;
            if (equipmentManager != null && equipmentData != null)
            {
                equipmentManager.RegisterEquipment(gameObject, equipmentData);
                equipmentInstance = equipmentManager.GetEquipmentInstance(gameObject);
            }
        }
        
        public override bool CanInteract(GameObject player)
        {
            if (!base.CanInteract(player) || !isOperational)
                return false;
            
            // Check if equipment has capacity for more operations
            if (equipmentInstance != null)
            {
                int capacity = GameManager.Instance.EquipmentManager.GetEquipmentCapacity(equipmentInstance);
                return currentRecipes.Count < capacity;
            }
            
            return !isInUse;
        }
        
        public override void Interact(GameObject player)
        {
            if (!CanInteract(player))
                return;
            
            // Show recipe selection UI or start default recipe
            ShowRecipeSelection();
        }
        
        protected virtual void ShowRecipeSelection()
        {
            var recipeManager = GameManager.Instance?.RecipeManager;
            if (recipeManager == null) return;
            
            // Get recipes that can be made with this equipment
            var availableRecipes = recipeManager.GetMakeableRecipes();
            var compatibleRecipes = new List<RecipeData>();
            
            foreach (var recipe in availableRecipes)
            {
                if (CanMakeRecipe(recipe))
                {
                    compatibleRecipes.Add(recipe);
                }
            }
            
            if (compatibleRecipes.Count > 0)
            {
                // For now, just start the first compatible recipe
                // TODO: Implement proper recipe selection UI
                StartRecipe(compatibleRecipes[0]);
            }
            else
            {
                Debug.Log($"No compatible recipes for {equipmentData.equipmentName}");
            }
        }
        
        protected virtual bool CanMakeRecipe(RecipeData recipe)
        {
            if (equipmentData == null) return false;
            
            // Check if this equipment type is required for the recipe
            if (recipe.requiredEquipment == equipmentData.equipmentType)
                return true;
            
            // Check if any recipe step requires this equipment
            foreach (var step in recipe.steps)
            {
                if (step.requiredEquipment == equipmentData.equipmentType)
                    return true;
            }
            
            return false;
        }
        
        public virtual bool StartRecipe(RecipeData recipe)
        {
            if (!CanMakeRecipe(recipe) || !CanInteract(null))
                return false;
            
            var recipeManager = GameManager.Instance?.RecipeManager;
            if (recipeManager == null) return false;
            
            // Start the recipe
            if (recipeManager.StartRecipe(recipe, transform))
            {
                var activeRecipe = recipeManager.GetActiveRecipeAt(transform);
                if (activeRecipe != null)
                {
                    currentRecipes.Add(activeRecipe);
                    
                    if (!isInUse)
                    {
                        StartOperation();
                    }
                    
                    Debug.Log($"Started recipe {recipe.recipeName} on {equipmentData.equipmentName}");
                    return true;
                }
            }
            
            return false;
        }
        
        protected virtual void StartOperation()
        {
            isInUse = true;
            operationProgress = 0f;
            
            // Visual feedback
            if (operationIndicator)
                operationIndicator.SetActive(true);
            
            if (operationParticles)
                operationParticles.Play();
            
            if (operationAudio)
                operationAudio.Play();
            
            OnEquipmentStarted?.Invoke(this);
        }
        
        protected virtual void UpdateOperation()
        {
            // Update progress based on equipment efficiency
            if (equipmentInstance != null && currentRecipes.Count > 0)
            {
                float efficiency = GameManager.Instance.EquipmentManager.GetEquipmentEfficiency(equipmentInstance);
                float progressRate = efficiency * Time.deltaTime;
                
                // Update all active recipes
                bool anyRecipeActive = false;
                for (int i = currentRecipes.Count - 1; i >= 0; i--)
                {
                    var recipe = currentRecipes[i];
                    if (recipe.isCompleted)
                    {
                        currentRecipes.RemoveAt(i);
                        OnRecipeCompleted(recipe);
                    }
                    else
                    {
                        anyRecipeActive = true;
                    }
                }
                
                // Stop operation if no active recipes
                if (!anyRecipeActive)
                {
                    StopOperation();
                }
                else
                {
                    // Update progress based on the first active recipe
                    operationProgress = currentRecipes[0].GetProgress();
                }
            }
        }
        
        protected virtual void OnRecipeCompleted(ActiveRecipe recipe)
        {
            Debug.Log($"Recipe {recipe.recipe.recipeName} completed on {equipmentData.equipmentName}");
            OnEquipmentCompleted?.Invoke(this);
        }
        
        protected virtual void StopOperation()
        {
            isInUse = false;
            operationProgress = 0f;
            
            // Visual feedback
            if (operationIndicator)
                operationIndicator.SetActive(false);
            
            if (operationParticles)
                operationParticles.Stop();
            
            if (operationAudio)
                operationAudio.Stop();
            
            OnEquipmentStopped?.Invoke(this);
        }
        
        protected virtual void UpdateVisualFeedback()
        {
            // Update any progress indicators, animations, etc.
            // This can be overridden by specific equipment types
        }
        
        public override string GetInteractionPrompt()
        {
            if (!isOperational)
                return "Equipment is not operational";
            
            if (!CanInteract(null))
                return "Equipment is at capacity";
            
            return $"Use {equipmentData.equipmentName}";
        }
        
        // Public getters
        public bool IsOperational => isOperational;
        public bool IsInUse => isInUse;
        public float OperationProgress => operationProgress;
        public EquipmentData GetEquipmentData() => equipmentData;
        public EquipmentInstance GetEquipmentInstance() => equipmentInstance;
        
        protected virtual void OnDestroy()
        {
            // Unregister from equipment manager
            var equipmentManager = GameManager.Instance?.EquipmentManager;
            if (equipmentManager != null)
            {
                equipmentManager.UnregisterEquipment(gameObject);
            }
        }
    }
}
