using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;

namespace BakeItOut.Equipment
{
    /// <summary>
    /// Mixing station for combining ingredients
    /// </summary>
    public class MixingStation : BaseEquipment
    {
        [Header("Mixing Station Specific")]
        public Transform mixingBowl;
        public Transform[] ingredientSlots;
        public GameObject mixingPaddle;
        public float mixingSpeed = 1f;
        
        [Header("Visual Effects")]
        public ParticleSystem mixingParticles;
        public AudioClip mixingSound;
        public AudioClip completionSound;
        
        [Header("Animation")]
        public Animator mixingAnimator;
        public string mixingAnimationTrigger = "StartMixing";
        
        private bool isMixing = false;
        private float mixingProgress = 0f;
        
        protected override void Start()
        {
            base.Start();
            InitializeMixingStation();
        }
        
        protected override void Update()
        {
            base.Update();
            UpdateMixingAnimation();
        }
        
        private void InitializeMixingStation()
        {
            // Initialize ingredient slots
            if (ingredientSlots == null || ingredientSlots.Length == 0)
            {
                // Create default slots if none assigned
                ingredientSlots = new Transform[4]; // Default 4 ingredient slots
                for (int i = 0; i < ingredientSlots.Length; i++)
                {
                    GameObject slot = new GameObject($"IngredientSlot_{i}");
                    slot.transform.SetParent(transform);
                    slot.transform.localPosition = Vector3.right * (i - 1.5f) * 0.3f;
                    ingredientSlots[i] = slot.transform;
                }
            }
            
            // Set mixing bowl position if not assigned
            if (mixingBowl == null)
            {
                mixingBowl = transform;
            }
        }
        
        protected override bool CanMakeRecipe(RecipeData recipe)
        {
            // Mixing station can make recipes that require mixing
            return recipe.requiredEquipment == EquipmentType.Mixer ||
                   System.Array.Exists(recipe.steps, step => step.requiredEquipment == EquipmentType.Mixer);
        }
        
        public override bool StartRecipe(RecipeData recipe)
        {
            if (!base.StartRecipe(recipe))
                return false;
            
            // Start mixing animation and effects
            StartMixing();
            return true;
        }
        
        private void StartMixing()
        {
            isMixing = true;
            mixingProgress = 0f;
            
            // Start mixing animation
            if (mixingAnimator != null)
            {
                mixingAnimator.SetTrigger(mixingAnimationTrigger);
                mixingAnimator.SetBool("IsMixing", true);
            }
            
            // Start mixing sound
            if (operationAudio && mixingSound)
            {
                operationAudio.clip = mixingSound;
                operationAudio.loop = true;
                operationAudio.Play();
            }
            
            // Start mixing particles
            if (mixingParticles)
                mixingParticles.Play();
        }
        
        protected override void UpdateOperation()
        {
            base.UpdateOperation();
            
            // Update mixing progress
            if (isMixing && currentRecipes.Count > 0)
            {
                float efficiency = 1f;
                if (equipmentInstance != null)
                {
                    efficiency = GameManager.Instance.EquipmentManager.GetEquipmentEfficiency(equipmentInstance);
                }
                
                mixingProgress = currentRecipes[0].GetProgress();
                
                // Update mixing speed based on efficiency
                if (mixingAnimator != null)
                {
                    mixingAnimator.speed = efficiency * mixingSpeed;
                }
            }
        }
        
        protected override void OnRecipeCompleted(ActiveRecipe recipe)
        {
            base.OnRecipeCompleted(recipe);
            
            // Play completion sound
            if (operationAudio && completionSound)
            {
                operationAudio.PlayOneShot(completionSound);
            }
            
            // Create mixed ingredients/dough
            CreateMixedResult(recipe);
        }
        
        private void CreateMixedResult(ActiveRecipe recipe)
        {
            // Spawn the mixed result at the mixing bowl
            if (mixingBowl != null && recipe.recipe.resultPrefab != null)
            {
                Vector3 spawnPosition = mixingBowl.position + Vector3.up * 0.1f;
                GameObject mixedResult = Instantiate(recipe.recipe.resultPrefab, spawnPosition, mixingBowl.rotation);
                
                // Initialize the mixed result
                var bakedGood = mixedResult.GetComponent<BakedGood>();
                if (bakedGood != null)
                {
                    bakedGood.Initialize(recipe.recipe, recipe.quality);
                }
                else
                {
                    // If it's not a final baked good, it might be an intermediate ingredient
                    var ingredient = mixedResult.GetComponent<IngredientItem>();
                    if (ingredient != null)
                    {
                        // TODO: Initialize intermediate ingredient
                    }
                }
            }
        }
        
        protected override void StopOperation()
        {
            base.StopOperation();
            
            // Stop mixing when no recipes are active
            if (currentRecipes.Count == 0)
            {
                StopMixing();
            }
        }
        
        private void StopMixing()
        {
            isMixing = false;
            mixingProgress = 0f;
            
            // Stop mixing animation
            if (mixingAnimator != null)
            {
                mixingAnimator.SetBool("IsMixing", false);
                mixingAnimator.speed = 1f; // Reset to normal speed
            }
            
            // Stop mixing particles
            if (mixingParticles)
                mixingParticles.Stop();
        }
        
        private void UpdateMixingAnimation()
        {
            // Update paddle rotation or other visual effects
            if (isMixing && mixingPaddle != null)
            {
                float rotationSpeed = 90f; // degrees per second
                if (equipmentInstance != null)
                {
                    float efficiency = GameManager.Instance.EquipmentManager.GetEquipmentEfficiency(equipmentInstance);
                    rotationSpeed *= efficiency;
                }
                
                mixingPaddle.transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
            }
        }
        
        public override string GetInteractionPrompt()
        {
            if (!isOperational)
                return "Mixing station is not operational";
            
            if (currentRecipes.Count >= GetMaxCapacity())
                return "Mixing station is busy";
            
            if (isMixing)
                return $"Use Mixing Station (Mixing: {mixingProgress * 100:F0}%)";
            
            return "Use Mixing Station";
        }
        
        private int GetMaxCapacity()
        {
            if (equipmentInstance != null)
                return GameManager.Instance.EquipmentManager.GetEquipmentCapacity(equipmentInstance);
            return 1; // Default capacity
        }
        
        // Method to add ingredients visually (for enhanced gameplay)
        public void AddIngredientVisual(IngredientData ingredient, int slotIndex)
        {
            if (ingredientSlots != null && slotIndex < ingredientSlots.Length)
            {
                Transform slot = ingredientSlots[slotIndex];
                if (slot != null && ingredient.prefab != null)
                {
                    // Spawn ingredient visual at slot
                    GameObject ingredientVisual = Instantiate(ingredient.prefab, slot.position, slot.rotation);
                    ingredientVisual.transform.SetParent(slot);
                    
                    // Scale down for visual representation
                    ingredientVisual.transform.localScale *= 0.5f;
                    
                    // Remove any colliders (it's just visual)
                    Collider[] colliders = ingredientVisual.GetComponentsInChildren<Collider>();
                    foreach (var col in colliders)
                    {
                        col.enabled = false;
                    }
                }
            }
        }
        
        // Method to clear ingredient visuals
        public void ClearIngredientVisuals()
        {
            if (ingredientSlots != null)
            {
                foreach (var slot in ingredientSlots)
                {
                    if (slot != null)
                    {
                        // Destroy all child objects (ingredient visuals)
                        for (int i = slot.childCount - 1; i >= 0; i--)
                        {
                            DestroyImmediate(slot.GetChild(i).gameObject);
                        }
                    }
                }
            }
        }
        
        // Public getters
        public bool IsMixing => isMixing;
        public float MixingProgress => mixingProgress;
    }
}
