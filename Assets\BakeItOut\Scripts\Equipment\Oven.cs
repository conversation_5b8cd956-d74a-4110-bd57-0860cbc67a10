using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;

namespace BakeItOut.Equipment
{
    /// <summary>
    /// Oven equipment for baking recipes
    /// </summary>
    public class Oven : BaseEquipment
    {
        [Header("Oven Specific")]
        public Transform[] bakingSlots;
        public GameObject[] slotIndicators;
        public Light ovenLight;
        public float maxTemperature = 250f;
        public float currentTemperature = 20f;
        public float heatingRate = 50f; // degrees per second
        
        [Header("Visual Effects")]
        public ParticleSystem heatParticles;
        public AudioClip heatingSound;
        public AudioClip bakingSound;
        public AudioClip completionSound;
        
        private bool isHeating = false;
        private float targetTemperature = 180f; // Default baking temperature
        
        protected override void Start()
        {
            base.Start();
            InitializeOven();
        }
        
        protected override void Update()
        {
            base.Update();
            UpdateTemperature();
            UpdateOvenVisuals();
        }
        
        private void InitializeOven()
        {
            // Initialize baking slots
            if (bakingSlots == null || bakingSlots.Length == 0)
            {
                // Create default slots if none assigned
                bakingSlots = new Transform[1];
                bakingSlots[0] = transform;
            }
            
            // Initialize slot indicators
            if (slotIndicators != null)
            {
                foreach (var indicator in slotIndicators)
                {
                    if (indicator) indicator.SetActive(false);
                }
            }
            
            // Set initial temperature
            currentTemperature = 20f; // Room temperature
        }
        
        protected override bool CanMakeRecipe(RecipeData recipe)
        {
            // Oven can make recipes that require baking
            return recipe.requiredEquipment == EquipmentType.Oven ||
                   System.Array.Exists(recipe.steps, step => step.requiredEquipment == EquipmentType.Oven);
        }
        
        public override bool StartRecipe(RecipeData recipe)
        {
            if (!base.StartRecipe(recipe))
                return false;
            
            // Start heating the oven
            StartHeating(GetRequiredTemperature(recipe));
            return true;
        }
        
        private float GetRequiredTemperature(RecipeData recipe)
        {
            // Different recipes might require different temperatures
            // For now, use a default temperature based on recipe type
            if (recipe.recipeName.ToLower().Contains("bread"))
                return 220f;
            else if (recipe.recipeName.ToLower().Contains("cake"))
                return 180f;
            else if (recipe.recipeName.ToLower().Contains("cookie"))
                return 200f;
            else
                return 180f; // Default temperature
        }
        
        private void StartHeating(float temperature)
        {
            targetTemperature = Mathf.Clamp(temperature, 20f, maxTemperature);
            isHeating = true;
            
            // Play heating sound
            if (operationAudio && heatingSound)
            {
                operationAudio.clip = heatingSound;
                operationAudio.loop = true;
                operationAudio.Play();
            }
            
            // Start heat particles
            if (heatParticles)
                heatParticles.Play();
        }
        
        private void UpdateTemperature()
        {
            if (isHeating)
            {
                // Heat up to target temperature
                if (currentTemperature < targetTemperature)
                {
                    float heatingSpeed = heatingRate;
                    if (equipmentInstance != null)
                    {
                        heatingSpeed *= GameManager.Instance.EquipmentManager.GetEquipmentEfficiency(equipmentInstance);
                    }
                    
                    currentTemperature += heatingSpeed * Time.deltaTime;
                    currentTemperature = Mathf.Min(currentTemperature, targetTemperature);
                }
                
                // Check if target temperature reached
                if (Mathf.Approximately(currentTemperature, targetTemperature))
                {
                    OnTargetTemperatureReached();
                }
            }
            else
            {
                // Cool down when not in use
                if (currentTemperature > 20f)
                {
                    currentTemperature -= (heatingRate * 0.3f) * Time.deltaTime; // Cool down slower
                    currentTemperature = Mathf.Max(currentTemperature, 20f);
                }
            }
        }
        
        private void OnTargetTemperatureReached()
        {
            // Switch to baking sound
            if (operationAudio && bakingSound)
            {
                operationAudio.clip = bakingSound;
                operationAudio.Play();
            }
        }
        
        protected override void UpdateOperation()
        {
            base.UpdateOperation();
            
            // Oven-specific operation updates
            if (isInUse && currentRecipes.Count > 0)
            {
                // Check if oven is at proper temperature
                bool atTemperature = currentTemperature >= (targetTemperature * 0.95f);
                
                if (!atTemperature)
                {
                    // Slow down cooking if not at temperature
                    // This is handled by the efficiency calculation in base class
                }
            }
        }
        
        protected override void OnRecipeCompleted(ActiveRecipe recipe)
        {
            base.OnRecipeCompleted(recipe);
            
            // Play completion sound
            if (operationAudio && completionSound)
            {
                operationAudio.PlayOneShot(completionSound);
            }
            
            // TODO: Spawn baked goods at appropriate slot
            SpawnBakedGoods(recipe);
        }
        
        private void SpawnBakedGoods(ActiveRecipe recipe)
        {
            // Find an available slot
            Transform spawnSlot = GetAvailableSlot();
            if (spawnSlot != null && recipe.recipe.resultPrefab != null)
            {
                Vector3 spawnPosition = spawnSlot.position + Vector3.up * 0.1f;
                GameObject bakedGood = Instantiate(recipe.recipe.resultPrefab, spawnPosition, spawnSlot.rotation);
                
                // Add any quality or other properties to the baked good
                var bakedItem = bakedGood.GetComponent<BakedGood>();
                if (bakedItem != null)
                {
                    bakedItem.Initialize(recipe.recipe, recipe.quality);
                }
            }
        }
        
        private Transform GetAvailableSlot()
        {
            // Return the first available slot
            // TODO: Implement proper slot management
            return bakingSlots.Length > 0 ? bakingSlots[0] : transform;
        }
        
        protected override void StopOperation()
        {
            base.StopOperation();
            
            // Stop heating when no recipes are active
            if (currentRecipes.Count == 0)
            {
                isHeating = false;
                
                // Stop heat particles
                if (heatParticles)
                    heatParticles.Stop();
            }
        }
        
        private void UpdateOvenVisuals()
        {
            // Update oven light based on temperature
            if (ovenLight)
            {
                float intensity = Mathf.Lerp(0f, 2f, currentTemperature / maxTemperature);
                ovenLight.intensity = intensity;
                
                // Change color based on temperature
                Color lightColor = Color.Lerp(Color.white, Color.red, currentTemperature / maxTemperature);
                ovenLight.color = lightColor;
            }
            
            // Update slot indicators
            if (slotIndicators != null)
            {
                for (int i = 0; i < slotIndicators.Length; i++)
                {
                    if (slotIndicators[i])
                    {
                        bool slotInUse = i < currentRecipes.Count;
                        slotIndicators[i].SetActive(slotInUse);
                    }
                }
            }
        }
        
        public override string GetInteractionPrompt()
        {
            if (!isOperational)
                return "Oven is not operational";
            
            if (currentRecipes.Count >= GetMaxCapacity())
                return "Oven is full";
            
            if (isHeating)
                return $"Use Oven (Heating: {currentTemperature:F0}°C)";
            
            return "Use Oven";
        }
        
        private int GetMaxCapacity()
        {
            if (equipmentInstance != null)
                return GameManager.Instance.EquipmentManager.GetEquipmentCapacity(equipmentInstance);
            return bakingSlots.Length;
        }
        
        // Public getters for UI/debugging
        public float CurrentTemperature => currentTemperature;
        public float TargetTemperature => targetTemperature;
        public bool IsHeating => isHeating;
    }
}
