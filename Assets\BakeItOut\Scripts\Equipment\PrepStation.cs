using UnityEngine;
using BakeItOut.Data;
using BakeItOut.Core;

namespace BakeItOut.Equipment
{
    /// <summary>
    /// Preparation station for ingredient processing and basic food prep
    /// </summary>
    public class PrepStation : BaseEquipment
    {
        [Header("Prep Station Specific")]
        public Transform workSurface;
        public Transform[] toolSlots;
        public GameObject[] availableTools; // Knives, cutting boards, etc.
        
        [Header("Processing")]
        public float baseProcessingTime = 10f;
        public IngredientType[] supportedIngredientTypes;
        
        [Header("Visual Effects")]
        public ParticleSystem choppingParticles;
        public AudioClip[] choppingSounds;
        public AudioClip completionSound;
        
        private bool isProcessing = false;
        private IngredientData currentIngredient;
        private GameObject currentTool;
        
        protected override void Start()
        {
            base.Start();
            InitializePrepStation();
        }
        
        protected override void Update()
        {
            base.Update();
            UpdateProcessingEffects();
        }
        
        private void InitializePrepStation()
        {
            // Initialize work surface
            if (workSurface == null)
            {
                workSurface = transform;
            }
            
            // Initialize tool slots
            if (toolSlots == null || toolSlots.Length == 0)
            {
                toolSlots = new Transform[3]; // Default 3 tool slots
                for (int i = 0; i < toolSlots.Length; i++)
                {
                    GameObject slot = new GameObject($"ToolSlot_{i}");
                    slot.transform.SetParent(transform);
                    slot.transform.localPosition = Vector3.right * (i - 1f) * 0.4f + Vector3.forward * 0.3f;
                    toolSlots[i] = slot.transform;
                }
            }
            
            // Set up default tools
            SetupDefaultTools();
            
            // Set supported ingredient types if not specified
            if (supportedIngredientTypes == null || supportedIngredientTypes.Length == 0)
            {
                supportedIngredientTypes = new IngredientType[]
                {
                    IngredientType.Fruits,
                    IngredientType.Nuts,
                    IngredientType.Spices,
                    IngredientType.Other
                };
            }
        }
        
        private void SetupDefaultTools()
        {
            // Place available tools in tool slots
            if (availableTools != null && toolSlots != null)
            {
                for (int i = 0; i < Mathf.Min(availableTools.Length, toolSlots.Length); i++)
                {
                    if (availableTools[i] != null && toolSlots[i] != null)
                    {
                        GameObject tool = Instantiate(availableTools[i], toolSlots[i].position, toolSlots[i].rotation);
                        tool.transform.SetParent(toolSlots[i]);
                    }
                }
            }
        }
        
        protected override bool CanMakeRecipe(RecipeData recipe)
        {
            // Prep station can make recipes that require preparation
            return recipe.requiredEquipment == EquipmentType.PrepStation ||
                   System.Array.Exists(recipe.steps, step => step.requiredEquipment == EquipmentType.PrepStation);
        }
        
        public override bool StartRecipe(RecipeData recipe)
        {
            if (!base.StartRecipe(recipe))
                return false;
            
            // Determine what ingredient we're processing
            currentIngredient = GetPrimaryIngredient(recipe);
            
            // Start processing
            StartProcessing();
            return true;
        }
        
        private IngredientData GetPrimaryIngredient(RecipeData recipe)
        {
            // Find the first ingredient that can be processed at this station
            foreach (var recipeIngredient in recipe.requiredIngredients)
            {
                if (CanProcessIngredient(recipeIngredient.ingredient))
                {
                    return recipeIngredient.ingredient;
                }
            }
            
            // Return first ingredient as fallback
            return recipe.requiredIngredients.Length > 0 ? recipe.requiredIngredients[0].ingredient : null;
        }
        
        private bool CanProcessIngredient(IngredientData ingredient)
        {
            if (ingredient == null) return false;
            
            // Check if this ingredient type is supported
            foreach (var supportedType in supportedIngredientTypes)
            {
                if (ingredient.type == supportedType)
                    return true;
            }
            
            return false;
        }
        
        private void StartProcessing()
        {
            isProcessing = true;
            
            // Select appropriate tool
            SelectTool();
            
            // Start processing sound
            if (operationAudio && choppingSounds != null && choppingSounds.Length > 0)
            {
                AudioClip soundToPlay = choppingSounds[Random.Range(0, choppingSounds.Length)];
                operationAudio.clip = soundToPlay;
                operationAudio.loop = true;
                operationAudio.Play();
            }
            
            // Start processing particles
            if (choppingParticles)
                choppingParticles.Play();
        }
        
        private void SelectTool()
        {
            // Select appropriate tool based on ingredient type
            if (currentIngredient != null && availableTools != null && availableTools.Length > 0)
            {
                int toolIndex = 0; // Default to first tool
                
                // Select tool based on ingredient type
                switch (currentIngredient.type)
                {
                    case IngredientType.Fruits:
                        toolIndex = 0; // Knife
                        break;
                    case IngredientType.Nuts:
                        toolIndex = 1; // Crusher/grinder
                        break;
                    case IngredientType.Spices:
                        toolIndex = 2; // Mortar and pestle
                        break;
                    default:
                        toolIndex = 0;
                        break;
                }
                
                toolIndex = Mathf.Clamp(toolIndex, 0, availableTools.Length - 1);
                currentTool = availableTools[toolIndex];
            }
        }
        
        protected override void UpdateOperation()
        {
            base.UpdateOperation();
            
            // Update processing effects based on progress
            if (isProcessing && currentRecipes.Count > 0)
            {
                float progress = currentRecipes[0].GetProgress();
                
                // Adjust particle emission based on progress
                if (choppingParticles != null)
                {
                    var emission = choppingParticles.emission;
                    emission.rateOverTime = Mathf.Lerp(5f, 20f, progress);
                }
                
                // Adjust audio pitch based on efficiency
                if (operationAudio != null && equipmentInstance != null)
                {
                    float efficiency = GameManager.Instance.EquipmentManager.GetEquipmentEfficiency(equipmentInstance);
                    operationAudio.pitch = Mathf.Lerp(0.8f, 1.2f, efficiency);
                }
            }
        }
        
        protected override void OnRecipeCompleted(ActiveRecipe recipe)
        {
            base.OnRecipeCompleted(recipe);
            
            // Play completion sound
            if (operationAudio && completionSound)
            {
                operationAudio.PlayOneShot(completionSound);
            }
            
            // Create processed result
            CreateProcessedResult(recipe);
        }
        
        private void CreateProcessedResult(ActiveRecipe recipe)
        {
            // Spawn the processed result on the work surface
            if (workSurface != null && recipe.recipe.resultPrefab != null)
            {
                Vector3 spawnPosition = workSurface.position + Vector3.up * 0.1f;
                GameObject processedResult = Instantiate(recipe.recipe.resultPrefab, spawnPosition, workSurface.rotation);
                
                // Initialize the processed result
                var bakedGood = processedResult.GetComponent<BakedGood>();
                if (bakedGood != null)
                {
                    bakedGood.Initialize(recipe.recipe, recipe.quality);
                }
            }
        }
        
        protected override void StopOperation()
        {
            base.StopOperation();
            
            // Stop processing when no recipes are active
            if (currentRecipes.Count == 0)
            {
                StopProcessing();
            }
        }
        
        private void StopProcessing()
        {
            isProcessing = false;
            currentIngredient = null;
            currentTool = null;
            
            // Stop processing particles
            if (choppingParticles)
                choppingParticles.Stop();
            
            // Reset audio pitch
            if (operationAudio)
                operationAudio.pitch = 1f;
        }
        
        private void UpdateProcessingEffects()
        {
            // Add visual effects for processing (tool movement, etc.)
            if (isProcessing && currentTool != null)
            {
                // Simple tool animation - could be enhanced with proper animations
                float bobAmount = 0.02f;
                float bobSpeed = 10f;
                
                if (equipmentInstance != null)
                {
                    float efficiency = GameManager.Instance.EquipmentManager.GetEquipmentEfficiency(equipmentInstance);
                    bobSpeed *= efficiency;
                }
                
                Vector3 originalPos = currentTool.transform.localPosition;
                originalPos.y += Mathf.Sin(Time.time * bobSpeed) * bobAmount;
                currentTool.transform.localPosition = originalPos;
            }
        }
        
        public override string GetInteractionPrompt()
        {
            if (!isOperational)
                return "Prep station is not operational";
            
            if (currentRecipes.Count >= GetMaxCapacity())
                return "Prep station is busy";
            
            if (isProcessing)
            {
                string ingredientName = currentIngredient != null ? currentIngredient.ingredientName : "ingredients";
                return $"Processing {ingredientName}...";
            }
            
            return "Use Prep Station";
        }
        
        private int GetMaxCapacity()
        {
            if (equipmentInstance != null)
                return GameManager.Instance.EquipmentManager.GetEquipmentCapacity(equipmentInstance);
            return 1; // Default capacity
        }
        
        // Public getters
        public bool IsProcessing => isProcessing;
        public IngredientData CurrentIngredient => currentIngredient;
        public GameObject CurrentTool => currentTool;
    }
}
