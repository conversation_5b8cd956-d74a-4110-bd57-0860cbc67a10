using UnityEngine;
using BakeItOut.Data;
using System.Collections.Generic;
using System.Linq;

namespace BakeItOut.Core
{
    public class EquipmentManager : MonoBehaviour
    {
        [Header("Equipment Database")]
        public EquipmentData[] allEquipment;
        
        // Equipment instances in the scene
        private List<EquipmentInstance> equipmentInstances = new List<EquipmentInstance>();
        
        // Events
        public System.Action<EquipmentInstance> OnEquipmentUpgraded;
        public System.Action<EquipmentInstance> OnEquipmentAutomated;
        public System.Action<EquipmentInstance> OnEquipmentPurchased;
        
        public void RegisterEquipment(GameObject equipmentObject, EquipmentData data)
        {
            var instance = new EquipmentInstance
            {
                gameObject = equipmentObject,
                data = data,
                currentLevel = 1,
                isAutomated = false,
                isOperational = true
            };
            
            equipmentInstances.Add(instance);
            Debug.Log($"Equipment registered: {data.equipmentName}");
        }
        
        public void UnregisterEquipment(GameObject equipmentObject)
        {
            var instance = equipmentInstances.FirstOrDefault(e => e.gameObject == equipmentObject);
            if (instance != null)
            {
                equipmentInstances.Remove(instance);
                Debug.Log($"Equipment unregistered: {instance.data.equipmentName}");
            }
        }
        
        public bool UpgradeEquipment(EquipmentInstance equipment)
        {
            if (equipment.currentLevel >= equipment.data.maxLevel)
                return false;
            
            float upgradeCost = equipment.data.GetUpgradeCost(equipment.currentLevel);
            if (upgradeCost < 0)
                return false;
            
            var playerManager = GameManager.Instance.PlayerManager;
            if (!playerManager.CanAfford(upgradeCost))
                return false;
            
            // Pay for upgrade
            playerManager.SpendMoney(upgradeCost);
            
            // Upgrade equipment
            equipment.currentLevel++;
            OnEquipmentUpgraded?.Invoke(equipment);
            
            Debug.Log($"Equipment upgraded: {equipment.data.equipmentName} to level {equipment.currentLevel}");
            return true;
        }
        
        public bool AutomateEquipment(EquipmentInstance equipment)
        {
            if (!equipment.data.canBeAutomated || equipment.isAutomated)
                return false;
            
            var playerManager = GameManager.Instance.PlayerManager;
            
            // Check level requirement
            if (equipment.currentLevel < equipment.data.automationUnlockLevel)
                return false;
            
            // Check if player can afford automation
            if (!playerManager.CanAfford(equipment.data.automationCost))
                return false;
            
            // Check if player has researched automation technology
            bool hasAutomationTech = playerManager.researchedTechnologies.Any(tech => 
                tech.enablesAutomation && tech.automationTarget == equipment.data.equipmentType);
            
            if (!hasAutomationTech)
                return false;
            
            // Pay for automation
            playerManager.SpendMoney(equipment.data.automationCost);
            
            // Enable automation
            equipment.isAutomated = true;
            OnEquipmentAutomated?.Invoke(equipment);
            
            Debug.Log($"Equipment automated: {equipment.data.equipmentName}");
            return true;
        }
        
        public bool PurchaseEquipment(EquipmentData equipmentData, Vector3 position, Quaternion rotation)
        {
            var playerManager = GameManager.Instance.PlayerManager;
            
            // Check if player has unlocked this equipment
            if (!playerManager.unlockedEquipment.Contains(equipmentData))
                return false;
            
            // For now, use a base cost (this could be expanded)
            float cost = equipmentData.GetUpgradeCost(1);
            if (cost < 0) cost = 100f; // Default cost
            
            if (!playerManager.CanAfford(cost))
                return false;
            
            // Pay for equipment
            playerManager.SpendMoney(cost);
            
            // Instantiate equipment
            GameObject equipmentObject = Instantiate(equipmentData.prefab, position, rotation);
            RegisterEquipment(equipmentObject, equipmentData);
            
            OnEquipmentPurchased?.Invoke(GetEquipmentInstance(equipmentObject));
            
            Debug.Log($"Equipment purchased: {equipmentData.equipmentName}");
            return true;
        }
        
        public EquipmentInstance GetEquipmentInstance(GameObject equipmentObject)
        {
            return equipmentInstances.FirstOrDefault(e => e.gameObject == equipmentObject);
        }
        
        public List<EquipmentInstance> GetEquipmentByType(EquipmentType type)
        {
            return equipmentInstances.Where(e => e.data.equipmentType == type).ToList();
        }
        
        public List<EquipmentInstance> GetAllEquipment()
        {
            return new List<EquipmentInstance>(equipmentInstances);
        }
        
        public List<EquipmentInstance> GetAutomatedEquipment()
        {
            return equipmentInstances.Where(e => e.isAutomated).ToList();
        }
        
        public float GetEquipmentEfficiency(EquipmentInstance equipment)
        {
            float efficiency = 1f;
            
            // Level bonus
            efficiency *= equipment.data.GetProcessingSpeedAtLevel(equipment.currentLevel);
            
            // Automation penalty
            if (equipment.isAutomated)
            {
                efficiency *= equipment.data.automationEfficiency;
            }
            
            // Technology bonuses
            var playerManager = GameManager.Instance.PlayerManager;
            foreach (var tech in playerManager.researchedTechnologies)
            {
                if (tech.affectsAllEquipment || tech.affectedEquipment == equipment.data.equipmentType)
                {
                    if (tech.technologyType == TechnologyType.SpeedIncrease)
                    {
                        efficiency += tech.effectValue;
                    }
                }
            }
            
            return efficiency;
        }
        
        public int GetEquipmentCapacity(EquipmentInstance equipment)
        {
            int capacity = equipment.data.GetCapacityAtLevel(equipment.currentLevel);
            
            // Technology bonuses
            var playerManager = GameManager.Instance.PlayerManager;
            foreach (var tech in playerManager.researchedTechnologies)
            {
                if (tech.affectsAllEquipment || tech.affectedEquipment == equipment.data.equipmentType)
                {
                    if (tech.technologyType == TechnologyType.CapacityIncrease)
                    {
                        capacity += Mathf.RoundToInt(tech.effectValue);
                    }
                }
            }
            
            return capacity;
        }
        
        public bool CanUseEquipment(EquipmentInstance equipment, RecipeData recipe)
        {
            // Check if equipment type matches recipe requirement
            foreach (var step in recipe.steps)
            {
                if (step.requiredEquipment == equipment.data.equipmentType)
                    return true;
            }
            
            return recipe.requiredEquipment == equipment.data.equipmentType;
        }
        
        public List<EquipmentInstance> GetAvailableEquipmentForRecipe(RecipeData recipe)
        {
            return equipmentInstances.Where(equipment => 
                CanUseEquipment(equipment, recipe) && 
                equipment.isOperational
            ).ToList();
        }
        
        private void Update()
        {
            // Handle automated equipment operations
            HandleAutomatedEquipment();
        }
        
        private void HandleAutomatedEquipment()
        {
            var automatedEquipment = GetAutomatedEquipment();
            var recipeManager = GameManager.Instance.RecipeManager;
            
            foreach (var equipment in automatedEquipment)
            {
                // Check if equipment is idle and can start a new recipe
                var activeRecipe = recipeManager.GetActiveRecipeAt(equipment.gameObject.transform);
                if (activeRecipe == null)
                {
                    // Try to start a new recipe automatically
                    TryStartAutomatedRecipe(equipment);
                }
            }
        }
        
        private void TryStartAutomatedRecipe(EquipmentInstance equipment)
        {
            var recipeManager = GameManager.Instance.RecipeManager;
            var availableRecipes = recipeManager.GetMakeableRecipes()
                .Where(recipe => CanUseEquipment(equipment, recipe))
                .ToList();
            
            if (availableRecipes.Count > 0)
            {
                // For now, just pick the first available recipe
                // This could be improved with priority systems
                var selectedRecipe = availableRecipes[0];
                recipeManager.StartRecipe(selectedRecipe, equipment.gameObject.transform);
            }
        }
    }
    
    [System.Serializable]
    public class EquipmentInstance
    {
        public GameObject gameObject;
        public EquipmentData data;
        public int currentLevel;
        public bool isAutomated;
        public bool isOperational;
        
        public float GetProcessingSpeed()
        {
            return data.GetProcessingSpeedAtLevel(currentLevel);
        }
        
        public int GetCapacity()
        {
            return data.GetCapacityAtLevel(currentLevel);
        }
    }
}
