using UnityEngine;
using BakeItOut.Data;
using System.Collections.Generic;
using System.Linq;

namespace BakeItOut.Core
{
    public class InventoryManager : MonoBehaviour
    {
        [Header("Inventory Settings")]
        public int maxInventorySlots = 50;
        
        // Inventory storage
        private Dictionary<IngredientData, InventoryItem> inventory = new Dictionary<IngredientData, InventoryItem>();
        
        // Events
        public System.Action<IngredientData, int> OnIngredientAdded;
        public System.Action<IngredientData, int> OnIngredientRemoved;
        public System.Action<IngredientData> OnIngredientSpoiled;
        public System.Action OnInventoryChanged;
        
        private void Update()
        {
            // Handle ingredient spoilage
            HandleSpoilage();
        }
        
        public bool AddIngredient(IngredientData ingredient, int quantity, Data.QualityLevel quality = Data.QualityLevel.Common)
        {
            if (ingredient == null || quantity <= 0)
                return false;
            
            // Check if we have space
            if (!HasSpaceForIngredient(ingredient, quantity))
                return false;
            
            if (inventory.ContainsKey(ingredient))
            {
                inventory[ingredient].quantity += quantity;
                // Update quality if better
                if (quality > inventory[ingredient].quality)
                    inventory[ingredient].quality = quality;
            }
            else
            {
                inventory[ingredient] = new InventoryItem
                {
                    ingredient = ingredient,
                    quantity = quantity,
                    quality = quality,
                    addedTime = Time.time
                };
            }
            
            OnIngredientAdded?.Invoke(ingredient, quantity);
            OnInventoryChanged?.Invoke();
            return true;
        }
        
        public bool RemoveIngredient(IngredientData ingredient, int quantity)
        {
            if (ingredient == null || quantity <= 0)
                return false;
            
            if (!inventory.ContainsKey(ingredient) || inventory[ingredient].quantity < quantity)
                return false;
            
            inventory[ingredient].quantity -= quantity;
            
            if (inventory[ingredient].quantity <= 0)
            {
                inventory.Remove(ingredient);
            }
            
            OnIngredientRemoved?.Invoke(ingredient, quantity);
            OnInventoryChanged?.Invoke();
            return true;
        }
        
        public int GetIngredientCount(IngredientData ingredient)
        {
            if (inventory.ContainsKey(ingredient))
                return inventory[ingredient].quantity;
            return 0;
        }
        
        public Data.QualityLevel GetIngredientQuality(IngredientData ingredient)
        {
            if (inventory.ContainsKey(ingredient))
                return inventory[ingredient].quality;
            return Data.QualityLevel.Poor;
        }
        
        public bool HasIngredient(IngredientData ingredient, int quantity = 1)
        {
            return GetIngredientCount(ingredient) >= quantity;
        }
        
        public bool HasIngredientsForRecipe(RecipeData recipe)
        {
            foreach (var recipeIngredient in recipe.requiredIngredients)
            {
                if (!HasIngredient(recipeIngredient.ingredient, recipeIngredient.quantity))
                    return false;
                
                // Check quality requirement
                if (GetIngredientQuality(recipeIngredient.ingredient) < recipeIngredient.minimumQuality)
                    return false;
            }
            return true;
        }
        
        public bool ConsumeIngredientsForRecipe(RecipeData recipe)
        {
            if (!HasIngredientsForRecipe(recipe))
                return false;
            
            foreach (var recipeIngredient in recipe.requiredIngredients)
            {
                RemoveIngredient(recipeIngredient.ingredient, recipeIngredient.quantity);
            }
            
            return true;
        }
        
        public List<InventoryItem> GetAllItems()
        {
            return inventory.Values.ToList();
        }
        
        public int GetTotalItemCount()
        {
            return inventory.Values.Sum(item => item.quantity);
        }
        
        public int GetUsedSlots()
        {
            return inventory.Count;
        }
        
        public int GetAvailableSlots()
        {
            return maxInventorySlots - GetUsedSlots();
        }
        
        private bool HasSpaceForIngredient(IngredientData ingredient, int quantity)
        {
            // If we already have this ingredient, we can stack it
            if (inventory.ContainsKey(ingredient))
            {
                int currentQuantity = inventory[ingredient].quantity;
                return currentQuantity + quantity <= ingredient.stackSize;
            }
            
            // If we don't have this ingredient, we need a free slot
            return GetAvailableSlots() > 0;
        }
        
        private void HandleSpoilage()
        {
            var spoiledItems = new List<IngredientData>();
            
            foreach (var kvp in inventory)
            {
                var ingredient = kvp.Key;
                var item = kvp.Value;
                
                if (ingredient.isPerishable)
                {
                    float timeSinceAdded = Time.time - item.addedTime;
                    if (timeSinceAdded >= ingredient.spoilTime)
                    {
                        spoiledItems.Add(ingredient);
                    }
                }
            }
            
            // Remove spoiled items
            foreach (var spoiledIngredient in spoiledItems)
            {
                int spoiledQuantity = inventory[spoiledIngredient].quantity;
                inventory.Remove(spoiledIngredient);
                OnIngredientSpoiled?.Invoke(spoiledIngredient);
                OnInventoryChanged?.Invoke();
                Debug.Log($"{spoiledQuantity} {spoiledIngredient.ingredientName} spoiled!");
            }
        }
        
        public void ClearInventory()
        {
            inventory.Clear();
            OnInventoryChanged?.Invoke();
        }
    }
    
    [System.Serializable]
    public class InventoryItem
    {
        public IngredientData ingredient;
        public int quantity;
        public Data.QualityLevel quality;
        public float addedTime;
    }
}
