using UnityEngine;
using BakeItOut.Data;
using System.Collections.Generic;
using System.Linq;

namespace BakeItOut.Core
{
    public class OrderManager : MonoBehaviour
    {
        [Header("Order Generation")]
        public OrderData[] availableOrders;
        public float baseOrderInterval = 60f; // 1 minute
        public int maxActiveOrders = 5;
        public float difficultyScaling = 0.1f;
        
        [Header("Current Orders")]
        public List<ActiveOrder> activeOrders = new List<ActiveOrder>();
        
        // Events
        public System.Action<ActiveOrder> OnOrderReceived;
        public System.Action<ActiveOrder> OnOrderCompleted;
        public System.Action<ActiveOrder> OnOrderFailed;
        public System.Action<ActiveOrder> OnOrderExpired;
        
        private float nextOrderTime;
        private bool isGeneratingOrders = false;
        
        private void Update()
        {
            if (isGeneratingOrders)
            {
                UpdateOrderGeneration();
                UpdateActiveOrders();
            }
        }
        
        public void StartOrderGeneration()
        {
            isGeneratingOrders = true;
            nextOrderTime = Time.time + GetNextOrderDelay();
        }
        
        public void StopOrderGeneration()
        {
            isGeneratingOrders = false;
        }
        
        private void UpdateOrderGeneration()
        {
            if (Time.time >= nextOrderTime && activeOrders.Count < maxActiveOrders)
            {
                GenerateNewOrder();
                nextOrderTime = Time.time + GetNextOrderDelay();
            }
        }
        
        private void UpdateActiveOrders()
        {
            var expiredOrders = new List<ActiveOrder>();
            
            foreach (var order in activeOrders)
            {
                order.remainingTime -= Time.deltaTime;
                
                if (order.remainingTime <= 0)
                {
                    expiredOrders.Add(order);
                }
            }
            
            // Handle expired orders
            foreach (var expiredOrder in expiredOrders)
            {
                ExpireOrder(expiredOrder);
            }
        }
        
        private void GenerateNewOrder()
        {
            if (availableOrders == null || availableOrders.Length == 0)
                return;
            
            var playerManager = GameManager.Instance.PlayerManager;
            int playerLevel = playerManager.currentLevel;
            
            // Filter orders based on player level and unlocked recipes
            var validOrders = availableOrders.Where(order => 
                order.minimumPlayerLevel <= playerLevel &&
                order.requiredItems.All(item => playerManager.CanMakeRecipe(item.recipe))
            ).ToArray();
            
            if (validOrders.Length == 0)
                return;
            
            // Select random order with difficulty bias
            OrderData selectedOrder = SelectOrderByDifficulty(validOrders, playerLevel);
            
            // Create active order
            var activeOrder = new ActiveOrder
            {
                orderData = selectedOrder,
                remainingTime = selectedOrder.timeLimit,
                startTime = Time.time,
                isCompleted = false,
                completedItems = new List<OrderItem>()
            };
            
            // Scale difficulty based on player level
            ScaleOrderDifficulty(activeOrder, playerLevel);
            
            activeOrders.Add(activeOrder);
            OnOrderReceived?.Invoke(activeOrder);
            
            Debug.Log($"New order received: {selectedOrder.customerName} wants {selectedOrder.orderDescription}");
        }
        
        private OrderData SelectOrderByDifficulty(OrderData[] validOrders, int playerLevel)
        {
            // Weight orders based on difficulty and player level
            var weightedOrders = new List<(OrderData order, float weight)>();
            
            foreach (var order in validOrders)
            {
                float weight = 1f;
                
                // Prefer orders close to player level
                int levelDiff = Mathf.Abs((int)order.difficulty - (playerLevel / 10));
                weight = Mathf.Max(0.1f, 1f - (levelDiff * 0.2f));
                
                weightedOrders.Add((order, weight));
            }
            
            // Select based on weights
            float totalWeight = weightedOrders.Sum(w => w.weight);
            float randomValue = Random.Range(0f, totalWeight);
            float currentWeight = 0f;
            
            foreach (var (order, weight) in weightedOrders)
            {
                currentWeight += weight;
                if (randomValue <= currentWeight)
                    return order;
            }
            
            return validOrders[Random.Range(0, validOrders.Length)];
        }
        
        private void ScaleOrderDifficulty(ActiveOrder order, int playerLevel)
        {
            float scalingFactor = 1f + (playerLevel * difficultyScaling);
            
            // Reduce time limit for higher level players
            order.remainingTime /= scalingFactor;
            
            // Increase quantity requirements
            foreach (var item in order.orderData.requiredItems)
            {
                if (playerLevel > 10)
                {
                    item.quantity = Mathf.RoundToInt(item.quantity * (1f + (playerLevel - 10) * 0.1f));
                }
            }
        }
        
        public bool CompleteOrder(ActiveOrder order, List<OrderItem> completedItems)
        {
            if (!activeOrders.Contains(order) || order.isCompleted)
                return false;
            
            // Validate completion
            if (!ValidateOrderCompletion(order, completedItems))
                return false;
            
            order.isCompleted = true;
            order.completedItems = completedItems;
            
            // Calculate rewards
            float completionTime = Time.time - order.startTime;
            float reward = order.orderData.CalculateReward(completedItems, completionTime, GameManager.Instance.isMultiplayer);
            int experience = order.orderData.CalculateExperience(completedItems, GameManager.Instance.isMultiplayer);
            
            // Give rewards to player
            var playerManager = GameManager.Instance.PlayerManager;
            playerManager.AddMoney(reward);
            playerManager.GainExperience(experience);
            
            activeOrders.Remove(order);
            OnOrderCompleted?.Invoke(order);
            
            Debug.Log($"Order completed! Reward: ${reward:F2}, Experience: {experience}");
            return true;
        }
        
        private bool ValidateOrderCompletion(ActiveOrder order, List<OrderItem> completedItems)
        {
            foreach (var requiredItem in order.orderData.requiredItems)
            {
                var completedItem = completedItems.FirstOrDefault(item => item.recipe == requiredItem.recipe);
                if (completedItem == null || completedItem.quantity < requiredItem.quantity)
                    return false;
                
                if (completedItem.quality < order.orderData.minimumQuality)
                    return false;
            }
            
            return true;
        }
        
        private void ExpireOrder(ActiveOrder order)
        {
            activeOrders.Remove(order);
            OnOrderExpired?.Invoke(order);
            Debug.Log($"Order expired: {order.orderData.customerName}");
        }
        
        private float GetNextOrderDelay()
        {
            int playerLevel = GameManager.Instance.PlayerManager.currentLevel;
            float delay = baseOrderInterval / (1f + playerLevel * 0.05f); // Faster orders as player levels up
            return Random.Range(delay * 0.7f, delay * 1.3f); // Add some randomness
        }
        
        public ActiveOrder GetOrderById(int id)
        {
            return activeOrders.FirstOrDefault(order => order.GetHashCode() == id);
        }
        
        public void CancelOrder(ActiveOrder order)
        {
            if (activeOrders.Contains(order))
            {
                activeOrders.Remove(order);
                OnOrderFailed?.Invoke(order);
            }
        }
    }
    
    [System.Serializable]
    public class ActiveOrder
    {
        public OrderData orderData;
        public float remainingTime;
        public float startTime;
        public bool isCompleted;
        public List<OrderItem> completedItems;
        
        public float GetProgress()
        {
            if (completedItems == null || orderData.requiredItems == null)
                return 0f;
            
            int totalRequired = orderData.requiredItems.Sum(item => item.quantity);
            int totalCompleted = completedItems.Sum(item => item.quantity);
            
            return totalRequired > 0 ? (float)totalCompleted / totalRequired : 0f;
        }
    }
}
