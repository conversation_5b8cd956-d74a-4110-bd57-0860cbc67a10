using UnityEngine;
using BakeItOut.Data;
using System.Collections.Generic;

namespace BakeItOut.Core
{
    public class PlayerManager : MonoBehaviour
    {
        [Header("Player Stats")]
        public int currentLevel = 1;
        public int currentExperience = 0;
        public float currentMoney = 100f;
        
        [Header("Unlocked Content")]
        public List<RecipeData> unlockedRecipes = new List<RecipeData>();
        public List<EquipmentData> unlockedEquipment = new List<EquipmentData>();
        public List<TechnologyData> researchedTechnologies = new List<TechnologyData>();
        
        // Events
        public System.Action<int> OnLevelUp;
        public System.Action<int> OnExperienceGained;
        public System.Action<float> OnMoneyChanged;
        public System.Action<RecipeData> OnRecipeUnlocked;
        public System.Action<EquipmentData> OnEquipmentUnlocked;
        public System.Action<TechnologyData> OnTechnologyResearched;
        
        private PlayerProgressionData progressionData;
        
        public void InitializePlayer(PlayerProgressionData data)
        {
            progressionData = data;
            
            // Set starting values
            currentMoney = data.startingMoney;
            
            // Add starting ingredients to inventory
            var inventoryManager = GameManager.Instance.InventoryManager;
            for (int i = 0; i < data.startingIngredients.Length; i++)
            {
                if (i < data.startingQuantities.Length)
                {
                    inventoryManager.AddIngredient(data.startingIngredients[i], data.startingQuantities[i]);
                }
            }
            
            // Unlock starting recipes
            foreach (var recipe in data.startingRecipes)
            {
                UnlockRecipe(recipe);
            }
            
            OnMoneyChanged?.Invoke(currentMoney);
        }
        
        public void GainExperience(int amount)
        {
            currentExperience += amount;
            OnExperienceGained?.Invoke(amount);
            
            // Check for level up
            int requiredExp = progressionData.GetExperienceRequired(currentLevel + 1);
            while (currentExperience >= requiredExp && currentLevel < progressionData.maxLevel)
            {
                LevelUp();
                requiredExp = progressionData.GetExperienceRequired(currentLevel + 1);
            }
        }
        
        private void LevelUp()
        {
            currentLevel++;
            OnLevelUp?.Invoke(currentLevel);
            
            // Apply level rewards
            var reward = progressionData.GetRewardForLevel(currentLevel);
            if (reward != null)
            {
                ApplyLevelReward(reward);
            }
            
            Debug.Log($"Player leveled up to level {currentLevel}!");
        }
        
        private void ApplyLevelReward(LevelReward reward)
        {
            // Money reward
            if (reward.moneyReward > 0)
            {
                AddMoney(reward.moneyReward);
            }
            
            // Recipe rewards
            foreach (var recipe in reward.unlockedRecipes)
            {
                UnlockRecipe(recipe);
            }
            
            // Equipment rewards
            foreach (var equipment in reward.unlockedEquipment)
            {
                UnlockEquipment(equipment);
            }
            
            // Technology rewards
            foreach (var technology in reward.unlockedTechnologies)
            {
                ResearchTechnology(technology);
            }
        }
        
        public bool SpendMoney(float amount)
        {
            if (currentMoney >= amount)
            {
                currentMoney -= amount;
                OnMoneyChanged?.Invoke(currentMoney);
                return true;
            }
            return false;
        }
        
        public void AddMoney(float amount)
        {
            currentMoney += amount;
            OnMoneyChanged?.Invoke(currentMoney);
        }
        
        public void UnlockRecipe(RecipeData recipe)
        {
            if (!unlockedRecipes.Contains(recipe))
            {
                unlockedRecipes.Add(recipe);
                OnRecipeUnlocked?.Invoke(recipe);
                Debug.Log($"Recipe unlocked: {recipe.recipeName}");
            }
        }
        
        public void UnlockEquipment(EquipmentData equipment)
        {
            if (!unlockedEquipment.Contains(equipment))
            {
                unlockedEquipment.Add(equipment);
                OnEquipmentUnlocked?.Invoke(equipment);
                Debug.Log($"Equipment unlocked: {equipment.equipmentName}");
            }
        }
        
        public void ResearchTechnology(TechnologyData technology)
        {
            if (!researchedTechnologies.Contains(technology))
            {
                researchedTechnologies.Add(technology);
                OnTechnologyResearched?.Invoke(technology);
                Debug.Log($"Technology researched: {technology.technologyName}");
            }
        }
        
        public bool CanAfford(float cost)
        {
            return currentMoney >= cost;
        }
        
        public bool CanMakeRecipe(RecipeData recipe)
        {
            return recipe.CanPlayerMake(currentLevel, unlockedRecipes);
        }
        
        public bool CanResearchTechnology(TechnologyData technology)
        {
            return technology.CanPlayerResearch(currentLevel, researchedTechnologies.ToArray());
        }
        
        public int GetExperienceToNextLevel()
        {
            if (currentLevel >= progressionData.maxLevel)
                return 0;
                
            return progressionData.GetExperienceRequired(currentLevel + 1) - currentExperience;
        }
        
        public float GetLevelProgress()
        {
            if (currentLevel >= progressionData.maxLevel)
                return 1f;
                
            int currentLevelExp = progressionData.GetExperienceRequired(currentLevel);
            int nextLevelExp = progressionData.GetExperienceRequired(currentLevel + 1);
            int expInCurrentLevel = currentExperience - currentLevelExp;
            int expNeededForLevel = nextLevelExp - currentLevelExp;
            
            return (float)expInCurrentLevel / expNeededForLevel;
        }
    }
}
