using UnityEngine;
using BakeItOut.Data;
using System.Collections.Generic;
using System.Linq;

namespace BakeItOut.Core
{
    public class RecipeManager : MonoBehaviour
    {
        [Header("Recipe Database")]
        public RecipeData[] allRecipes;
        
        // Events
        public System.Action<RecipeData> OnRecipeStarted;
        public System.Action<RecipeData, Data.QualityLevel> OnRecipeCompleted;
        public System.Action<RecipeData> OnRecipeFailed;
        
        // Active recipes being prepared
        private List<ActiveRecipe> activeRecipes = new List<ActiveRecipe>();
        
        public bool CanStartRecipe(RecipeData recipe)
        {
            var playerManager = GameManager.Instance.PlayerManager;
            var inventoryManager = GameManager.Instance.InventoryManager;
            
            // Check if player can make this recipe
            if (!playerManager.CanMakeRecipe(recipe))
                return false;
            
            // Check if we have the required ingredients
            if (!inventoryManager.HasIngredientsForRecipe(recipe))
                return false;
            
            return true;
        }
        
        public bool StartRecipe(RecipeData recipe, Transform equipmentTransform = null)
        {
            if (!CanStartRecipe(recipe))
                return false;
            
            var inventoryManager = GameManager.Instance.InventoryManager;
            
            // Consume ingredients
            if (!inventoryManager.ConsumeIngredientsForRecipe(recipe))
                return false;
            
            // Create active recipe
            var activeRecipe = new ActiveRecipe
            {
                recipe = recipe,
                startTime = Time.time,
                currentStep = 0,
                isCompleted = false,
                equipmentTransform = equipmentTransform,
                quality = CalculateBaseQuality(recipe, inventoryManager)
            };
            
            activeRecipes.Add(activeRecipe);
            OnRecipeStarted?.Invoke(recipe);
            
            Debug.Log($"Started recipe: {recipe.recipeName}");
            return true;
        }
        
        public void UpdateRecipes()
        {
            var completedRecipes = new List<ActiveRecipe>();
            
            foreach (var activeRecipe in activeRecipes)
            {
                if (UpdateActiveRecipe(activeRecipe))
                {
                    completedRecipes.Add(activeRecipe);
                }
            }
            
            // Handle completed recipes
            foreach (var completedRecipe in completedRecipes)
            {
                CompleteRecipe(completedRecipe);
            }
        }
        
        private bool UpdateActiveRecipe(ActiveRecipe activeRecipe)
        {
            float elapsedTime = Time.time - activeRecipe.startTime;
            float totalTime = activeRecipe.recipe.preparationTime + activeRecipe.recipe.cookingTime;
            
            // Check if recipe is completed
            if (elapsedTime >= totalTime)
            {
                return true;
            }
            
            // Update current step based on time
            float stepTime = totalTime / activeRecipe.recipe.steps.Length;
            int newStep = Mathf.FloorToInt(elapsedTime / stepTime);
            
            if (newStep != activeRecipe.currentStep && newStep < activeRecipe.recipe.steps.Length)
            {
                activeRecipe.currentStep = newStep;
                // Handle step-specific logic here if needed
            }
            
            return false;
        }
        
        private void CompleteRecipe(ActiveRecipe activeRecipe)
        {
            activeRecipes.Remove(activeRecipe);
            activeRecipe.isCompleted = true;
            
            // Calculate final quality based on timing and player skill
            Data.QualityLevel finalQuality = CalculateFinalQuality(activeRecipe);
            
            // Add completed item to inventory (or handle it differently)
            // For now, we'll just trigger the event
            OnRecipeCompleted?.Invoke(activeRecipe.recipe, finalQuality);
            
            // Give experience to player
            var playerManager = GameManager.Instance.PlayerManager;
            int experience = Mathf.RoundToInt(activeRecipe.recipe.baseExperience * (float)finalQuality);
            playerManager.GainExperience(experience);
            
            Debug.Log($"Recipe completed: {activeRecipe.recipe.recipeName} (Quality: {finalQuality})");
        }
        
        private Data.QualityLevel CalculateBaseQuality(RecipeData recipe, InventoryManager inventoryManager)
        {
            float totalQuality = 0f;
            int ingredientCount = 0;

            // Calculate average ingredient quality
            foreach (var ingredient in recipe.requiredIngredients)
            {
                Data.QualityLevel ingredientQuality = inventoryManager.GetIngredientQuality(ingredient.ingredient);
                totalQuality += (float)ingredientQuality;
                ingredientCount++;
            }

            if (ingredientCount == 0)
                return Data.QualityLevel.Common;

            float averageQuality = totalQuality / ingredientCount;
            return (Data.QualityLevel)Mathf.RoundToInt(averageQuality);
        }
        
        private Data.QualityLevel CalculateFinalQuality(ActiveRecipe activeRecipe)
        {
            float qualityModifier = 1f;

            // Base quality from ingredients
            float baseQuality = (float)activeRecipe.quality;

            // Player level bonus
            var playerManager = GameManager.Instance.PlayerManager;
            float levelBonus = playerManager.currentLevel * 0.02f; // 2% per level
            qualityModifier += levelBonus;

            // Technology bonuses
            foreach (var tech in playerManager.researchedTechnologies)
            {
                if (tech.technologyType == TechnologyType.QualityImprovement)
                {
                    qualityModifier += tech.effectValue;
                }
            }

            // Timing bonus/penalty (perfect timing gives bonus)
            float elapsedTime = Time.time - activeRecipe.startTime;
            float expectedTime = activeRecipe.recipe.preparationTime + activeRecipe.recipe.cookingTime;
            float timingAccuracy = 1f - Mathf.Abs(elapsedTime - expectedTime) / expectedTime;
            qualityModifier += timingAccuracy * 0.2f; // Up to 20% bonus for perfect timing

            // Apply quality modifier
            float finalQuality = baseQuality * qualityModifier;

            // Clamp to valid quality range
            finalQuality = Mathf.Clamp(finalQuality, 1f, 5f);

            return (Data.QualityLevel)Mathf.RoundToInt(finalQuality);
        }
        
        public List<RecipeData> GetAvailableRecipes()
        {
            var playerManager = GameManager.Instance.PlayerManager;
            return allRecipes.Where(recipe => playerManager.CanMakeRecipe(recipe)).ToList();
        }
        
        public List<RecipeData> GetMakeableRecipes()
        {
            return GetAvailableRecipes().Where(recipe => CanStartRecipe(recipe)).ToList();
        }
        
        public ActiveRecipe GetActiveRecipeAt(Transform equipment)
        {
            return activeRecipes.FirstOrDefault(recipe => recipe.equipmentTransform == equipment);
        }
        
        public List<ActiveRecipe> GetAllActiveRecipes()
        {
            return new List<ActiveRecipe>(activeRecipes);
        }
        
        public void CancelRecipe(ActiveRecipe activeRecipe)
        {
            if (activeRecipes.Contains(activeRecipe))
            {
                activeRecipes.Remove(activeRecipe);
                OnRecipeFailed?.Invoke(activeRecipe.recipe);
                Debug.Log($"Recipe cancelled: {activeRecipe.recipe.recipeName}");
            }
        }
        
        private void Update()
        {
            UpdateRecipes();
        }
    }
    
    [System.Serializable]
    public class ActiveRecipe
    {
        public RecipeData recipe;
        public float startTime;
        public int currentStep;
        public bool isCompleted;
        public Transform equipmentTransform;
        public QualityLevel quality;
        
        public float GetProgress()
        {
            float elapsedTime = Time.time - startTime;
            float totalTime = recipe.preparationTime + recipe.cookingTime;
            return Mathf.Clamp01(elapsedTime / totalTime);
        }
        
        public float GetRemainingTime()
        {
            float elapsedTime = Time.time - startTime;
            float totalTime = recipe.preparationTime + recipe.cookingTime;
            return Mathf.Max(0f, totalTime - elapsedTime);
        }
    }
}
