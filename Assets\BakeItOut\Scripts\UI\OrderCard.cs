using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BakeItOut.Core;
using BakeItOut.Data;

namespace BakeItOut.UI
{
    /// <summary>
    /// Individual order card UI component
    /// </summary>
    public class OrderCard : MonoBehaviour
    {
        [Header("UI Components")]
        public Image customerAvatar;
        public TextMeshProUGUI customerName;
        public TextMeshProUGUI orderDescription;
        public Slider timeSlider;
        public TextMeshProUGUI timeText;
        public Transform itemsContainer;
        public GameObject orderItemPrefab;
        public TextMeshProUGUI rewardText;
        public Button acceptButton;
        public Button cancelButton;
        
        [Header("Visual States")]
        public Color normalColor = Color.white;
        public Color urgentColor = Color.red;
        public Color completedColor = Color.green;
        public Image backgroundImage;
        
        [Header("Progress")]
        public Slider progressSlider;
        public TextMeshProUGUI progressText;
        
        // Order data
        private ActiveOrder activeOrder;
        private bool isAccepted = false;
        
        // Events
        public System.Action<OrderCard> OnOrderAccepted;
        public System.Action<OrderCard> OnOrderCancelled;
        public System.Action<OrderCard> OnOrderCompleted;
        
        private void Start()
        {
            SetupEventListeners();
        }
        
        private void Update()
        {
            if (activeOrder != null && isAccepted)
            {
                UpdateTimer();
                UpdateProgress();
            }
        }
        
        private void SetupEventListeners()
        {
            if (acceptButton)
                acceptButton.onClick.AddListener(AcceptOrder);
            
            if (cancelButton)
                cancelButton.onClick.AddListener(CancelOrder);
        }
        
        public void Initialize(ActiveOrder order)
        {
            activeOrder = order;
            isAccepted = false;
            
            UpdateDisplay();
        }
        
        private void UpdateDisplay()
        {
            if (activeOrder?.orderData == null) return;
            
            var orderData = activeOrder.orderData;
            
            // Customer info
            if (customerAvatar && orderData.customerAvatar)
                customerAvatar.sprite = orderData.customerAvatar;
            
            if (customerName)
                customerName.text = orderData.customerName;
            
            if (orderDescription)
                orderDescription.text = orderData.orderDescription;
            
            // Time setup
            if (timeSlider)
            {
                timeSlider.maxValue = orderData.timeLimit;
                timeSlider.value = activeOrder.remainingTime;
            }
            
            UpdateTimer();
            
            // Create item requirement displays
            CreateItemDisplays();
            
            // Reward info
            UpdateRewardDisplay();
            
            // Button states
            UpdateButtonStates();
            
            // Progress
            UpdateProgress();
        }
        
        private void CreateItemDisplays()
        {
            if (itemsContainer == null || orderItemPrefab == null) return;
            
            // Clear existing items
            foreach (Transform child in itemsContainer)
            {
                Destroy(child.gameObject);
            }
            
            // Create item displays
            foreach (var requiredItem in activeOrder.orderData.requiredItems)
            {
                GameObject itemObj = Instantiate(orderItemPrefab, itemsContainer);
                OrderItemDisplay itemDisplay = itemObj.GetComponent<OrderItemDisplay>();
                
                if (itemDisplay != null)
                {
                    itemDisplay.Initialize(requiredItem, activeOrder.orderData.minimumQuality);
                }
            }
        }
        
        private void UpdateTimer()
        {
            if (activeOrder == null) return;
            
            // Update time slider
            if (timeSlider)
            {
                timeSlider.value = activeOrder.remainingTime;
            }
            
            // Update time text
            if (timeText)
            {
                int minutes = Mathf.FloorToInt(activeOrder.remainingTime / 60f);
                int seconds = Mathf.FloorToInt(activeOrder.remainingTime % 60f);
                timeText.text = $"{minutes:00}:{seconds:00}";
            }
            
            // Update visual state based on urgency
            UpdateUrgencyVisuals();
        }
        
        private void UpdateUrgencyVisuals()
        {
            if (backgroundImage == null || activeOrder == null) return;
            
            float timeRatio = activeOrder.remainingTime / activeOrder.orderData.timeLimit;
            
            if (timeRatio <= 0.2f) // Less than 20% time remaining
            {
                backgroundImage.color = urgentColor;
            }
            else if (activeOrder.isCompleted)
            {
                backgroundImage.color = completedColor;
            }
            else
            {
                backgroundImage.color = normalColor;
            }
        }
        
        private void UpdateProgress()
        {
            if (activeOrder == null) return;
            
            float progress = activeOrder.GetProgress();
            
            if (progressSlider)
            {
                progressSlider.value = progress;
            }
            
            if (progressText)
            {
                progressText.text = $"{progress * 100:F0}%";
            }
            
            // Check if order is completed
            if (progress >= 1f && !activeOrder.isCompleted)
            {
                CompleteOrder();
            }
        }
        
        private void UpdateRewardDisplay()
        {
            if (rewardText == null || activeOrder?.orderData == null) return;
            
            var orderData = activeOrder.orderData;
            
            // Calculate potential reward
            float baseReward = orderData.basePayment;
            int baseExp = orderData.baseExperience;
            
            // Show difficulty indicator
            string difficultyText = GetDifficultyText(orderData.difficulty);
            
            rewardText.text = $"${baseReward:F0} • {baseExp} XP\n{difficultyText}";
        }
        
        private string GetDifficultyText(OrderDifficulty difficulty)
        {
            switch (difficulty)
            {
                case OrderDifficulty.Easy: return "★☆☆☆☆";
                case OrderDifficulty.Medium: return "★★☆☆☆";
                case OrderDifficulty.Hard: return "★★★☆☆";
                case OrderDifficulty.Expert: return "★★★★☆";
                case OrderDifficulty.Master: return "★★★★★";
                default: return "★☆☆☆☆";
            }
        }
        
        private void UpdateButtonStates()
        {
            if (acceptButton)
            {
                acceptButton.gameObject.SetActive(!isAccepted);
                acceptButton.interactable = CanAcceptOrder();
            }
            
            if (cancelButton)
            {
                cancelButton.gameObject.SetActive(isAccepted && !activeOrder.isCompleted);
            }
        }
        
        private bool CanAcceptOrder()
        {
            if (activeOrder?.orderData == null) return false;
            
            // Check if player has required level
            var playerManager = GameManager.Instance?.PlayerManager;
            if (playerManager != null)
            {
                return playerManager.currentLevel >= activeOrder.orderData.minimumPlayerLevel;
            }
            
            return true;
        }
        
        private void AcceptOrder()
        {
            if (!CanAcceptOrder()) return;
            
            isAccepted = true;
            UpdateButtonStates();
            OnOrderAccepted?.Invoke(this);
            
            Debug.Log($"Accepted order from {activeOrder.orderData.customerName}");
        }
        
        private void CancelOrder()
        {
            OnOrderCancelled?.Invoke(this);
            Debug.Log($"Cancelled order from {activeOrder.orderData.customerName}");
        }
        
        private void CompleteOrder()
        {
            if (activeOrder.isCompleted) return;
            
            activeOrder.isCompleted = true;
            UpdateUrgencyVisuals();
            UpdateButtonStates();
            OnOrderCompleted?.Invoke(this);
            
            Debug.Log($"Completed order from {activeOrder.orderData.customerName}");
        }
        
        // Public getters
        public ActiveOrder GetActiveOrder() => activeOrder;
        public bool IsAccepted => isAccepted;
        public bool IsCompleted => activeOrder?.isCompleted ?? false;
        
        // Public methods
        public void SetAccepted(bool accepted)
        {
            isAccepted = accepted;
            UpdateButtonStates();
        }
        
        public void RefreshDisplay()
        {
            UpdateDisplay();
        }
    }
    
    /// <summary>
    /// Individual order item requirement display
    /// </summary>
    public class OrderItemDisplay : MonoBehaviour
    {
        [Header("UI Components")]
        public Image itemIcon;
        public TextMeshProUGUI itemName;
        public TextMeshProUGUI quantityText;
        public Image qualityIndicator;
        public GameObject completedCheckmark;
        
        private OrderItem orderItem;
        private Data.QualityLevel minimumQuality;

        public void Initialize(OrderItem item, Data.QualityLevel minQuality)
        {
            orderItem = item;
            minimumQuality = minQuality;
            
            UpdateDisplay();
        }
        
        private void UpdateDisplay()
        {
            if (orderItem?.recipe == null) return;
            
            // Item icon
            if (itemIcon && orderItem.recipe.icon)
                itemIcon.sprite = orderItem.recipe.icon;
            
            // Item name
            if (itemName)
                itemName.text = orderItem.recipe.recipeName;
            
            // Quantity
            if (quantityText)
                quantityText.text = $"x{orderItem.quantity}";
            
            // Quality indicator
            if (qualityIndicator)
            {
                Color qualityColor = GetQualityColor(minimumQuality);
                qualityIndicator.color = qualityColor;
            }
            
            // Completion status
            if (completedCheckmark)
                completedCheckmark.SetActive(orderItem.isCompleted);
        }
        
        private Color GetQualityColor(Data.QualityLevel quality)
        {
            switch (quality)
            {
                case Data.QualityLevel.Poor: return Color.gray;
                case Data.QualityLevel.Common: return Color.white;
                case Data.QualityLevel.Good: return Color.green;
                case Data.QualityLevel.Excellent: return Color.blue;
                case Data.QualityLevel.Perfect: return Color.yellow;
                default: return Color.white;
            }
        }
        
        public void SetCompleted(bool completed)
        {
            orderItem.isCompleted = completed;
            UpdateDisplay();
        }
    }
}
