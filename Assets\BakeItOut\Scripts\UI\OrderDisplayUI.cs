using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BakeItOut.Core;
using BakeItOut.Data;
using System.Collections.Generic;

namespace BakeItOut.UI
{
    /// <summary>
    /// Main order display UI controller
    /// </summary>
    public class OrderDisplayUI : MonoBehaviour
    {
        [Header("UI References")]
        public GameObject orderPanel;
        public Transform orderContainer;
        public GameObject orderCardPrefab;
        public ScrollRect orderScrollRect;
        
        [<PERSON>er("Header Info")]
        public TextMeshProUGUI activeOrdersText;
        public TextMeshProUGUI totalEarningsText;
        public Button toggleButton;
        
        [Header("Filters")]
        public Dropdown difficultyFilter;
        public Toggle showCompletedToggle;
        public Button refreshButton;
        
        [Header("Settings")]
        public int maxDisplayedOrders = 10;
        public bool autoRefresh = true;
        public float refreshInterval = 1f;
        
        // Order management
        private List<OrderCard> orderCards = new List<OrderCard>();
        private OrderManager orderManager;
        private float lastRefreshTime;
        
        // Filtering
        private OrderDifficulty filterDifficulty = OrderDifficulty.Easy; // "All" filter
        private bool showCompleted = true;
        
        private void Start()
        {
            InitializeUI();
            SetupEventListeners();
            
            // Get order manager reference
            orderManager = GameManager.Instance?.OrderManager;
            if (orderManager != null)
            {
                orderManager.OnOrderReceived += OnOrderReceived;
                orderManager.OnOrderCompleted += OnOrderCompleted;
                orderManager.OnOrderExpired += OnOrderExpired;
            }
            
            RefreshOrderDisplay();
        }
        
        private void Update()
        {
            if (autoRefresh && Time.time - lastRefreshTime >= refreshInterval)
            {
                RefreshOrderDisplay();
                lastRefreshTime = Time.time;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (orderManager != null)
            {
                orderManager.OnOrderReceived -= OnOrderReceived;
                orderManager.OnOrderCompleted -= OnOrderCompleted;
                orderManager.OnOrderExpired -= OnOrderExpired;
            }
        }
        
        private void InitializeUI()
        {
            if (orderPanel)
                orderPanel.SetActive(true);
            
            UpdateHeaderInfo();
        }
        
        private void SetupEventListeners()
        {
            if (toggleButton)
                toggleButton.onClick.AddListener(ToggleOrderPanel);
            
            if (refreshButton)
                refreshButton.onClick.AddListener(() => RefreshOrderDisplay());
            
            if (difficultyFilter)
                difficultyFilter.onValueChanged.AddListener(OnDifficultyFilterChanged);
            
            if (showCompletedToggle)
                showCompletedToggle.onValueChanged.AddListener(OnShowCompletedChanged);
        }
        
        public void RefreshOrderDisplay()
        {
            if (orderManager == null) return;
            
            // Clear existing order cards
            ClearOrderCards();
            
            // Get filtered orders
            var filteredOrders = GetFilteredOrders();
            
            // Create order cards
            foreach (var order in filteredOrders)
            {
                CreateOrderCard(order);
            }
            
            UpdateHeaderInfo();
            lastRefreshTime = Time.time;
        }
        
        private void ClearOrderCards()
        {
            foreach (var card in orderCards)
            {
                if (card != null)
                    Destroy(card.gameObject);
            }
            orderCards.Clear();
        }
        
        private List<ActiveOrder> GetFilteredOrders()
        {
            var allOrders = orderManager.activeOrders;
            var filtered = new List<ActiveOrder>();
            
            foreach (var order in allOrders)
            {
                bool passesFilter = true;
                
                // Difficulty filter (if not "All")
                if (filterDifficulty != OrderDifficulty.Easy) // Using Easy as "All" placeholder
                {
                    passesFilter &= order.orderData.difficulty == filterDifficulty;
                }
                
                // Completed filter
                if (!showCompleted)
                {
                    passesFilter &= !order.isCompleted;
                }
                
                if (passesFilter)
                    filtered.Add(order);
            }
            
            // Limit to max displayed orders
            if (filtered.Count > maxDisplayedOrders)
            {
                filtered = filtered.GetRange(0, maxDisplayedOrders);
            }
            
            return filtered;
        }
        
        private void CreateOrderCard(ActiveOrder order)
        {
            if (orderContainer == null || orderCardPrefab == null) return;
            
            GameObject cardObj = Instantiate(orderCardPrefab, orderContainer);
            OrderCard orderCard = cardObj.GetComponent<OrderCard>();
            
            if (orderCard != null)
            {
                orderCard.Initialize(order);
                
                // Subscribe to card events
                orderCard.OnOrderAccepted += OnOrderCardAccepted;
                orderCard.OnOrderCancelled += OnOrderCardCancelled;
                orderCard.OnOrderCompleted += OnOrderCardCompleted;
                
                orderCards.Add(orderCard);
            }
        }
        
        private void UpdateHeaderInfo()
        {
            if (orderManager == null) return;
            
            // Active orders count
            if (activeOrdersText)
            {
                int activeCount = 0;
                int completedCount = 0;
                
                foreach (var order in orderManager.activeOrders)
                {
                    if (order.isCompleted)
                        completedCount++;
                    else
                        activeCount++;
                }
                
                activeOrdersText.text = $"Active: {activeCount} | Completed: {completedCount}";
            }
            
            // Total earnings (this would need to be tracked separately)
            if (totalEarningsText)
            {
                var playerManager = GameManager.Instance?.PlayerManager;
                if (playerManager != null)
                {
                    totalEarningsText.text = $"Total: ${playerManager.currentMoney:F2}";
                }
            }
        }
        
        private void OnOrderReceived(ActiveOrder order)
        {
            // Add new order card if it passes filters
            var filteredOrders = GetFilteredOrders();
            if (filteredOrders.Contains(order))
            {
                CreateOrderCard(order);
                UpdateHeaderInfo();
            }
        }
        
        private void OnOrderCompleted(ActiveOrder order)
        {
            // Find and update the corresponding order card
            var card = orderCards.Find(c => c.GetActiveOrder() == order);
            if (card != null)
            {
                card.RefreshDisplay();
            }
            
            UpdateHeaderInfo();
        }
        
        private void OnOrderExpired(ActiveOrder order)
        {
            // Remove the expired order card
            var card = orderCards.Find(c => c.GetActiveOrder() == order);
            if (card != null)
            {
                orderCards.Remove(card);
                Destroy(card.gameObject);
            }
            
            UpdateHeaderInfo();
        }
        
        private void OnOrderCardAccepted(OrderCard card)
        {
            Debug.Log($"Order accepted: {card.GetActiveOrder().orderData.customerName}");
            // Could add visual feedback, sound effects, etc.
        }
        
        private void OnOrderCardCancelled(OrderCard card)
        {
            var order = card.GetActiveOrder();
            if (orderManager != null)
            {
                orderManager.CancelOrder(order);
            }
            
            // Remove card from display
            orderCards.Remove(card);
            Destroy(card.gameObject);
            UpdateHeaderInfo();
        }
        
        private void OnOrderCardCompleted(OrderCard card)
        {
            var order = card.GetActiveOrder();
            if (orderManager != null)
            {
                // TODO: Implement proper order completion with items
                // For now, just mark as completed
                var completedItems = new List<OrderItem>();
                foreach (var requiredItem in order.orderData.requiredItems)
                {
                    completedItems.Add(new OrderItem
                    {
                        recipe = requiredItem.recipe,
                        quantity = requiredItem.quantity,
                        quality = order.orderData.minimumQuality,
                        isCompleted = true
                    });
                }
                
                orderManager.CompleteOrder(order, completedItems);
            }
        }
        
        public void ToggleOrderPanel()
        {
            if (orderPanel)
            {
                orderPanel.SetActive(!orderPanel.activeSelf);
            }
        }
        
        private void OnDifficultyFilterChanged(int value)
        {
            filterDifficulty = (OrderDifficulty)value;
            RefreshOrderDisplay();
        }
        
        private void OnShowCompletedChanged(bool show)
        {
            showCompleted = show;
            RefreshOrderDisplay();
        }
        
        // Public methods
        public void ShowOrderPanel()
        {
            if (orderPanel)
                orderPanel.SetActive(true);
        }
        
        public void HideOrderPanel()
        {
            if (orderPanel)
                orderPanel.SetActive(false);
        }
        
        public bool IsVisible => orderPanel != null && orderPanel.activeSelf;
        public int GetDisplayedOrderCount() => orderCards.Count;
        
        // Method to manually complete an order (for testing)
        public void CompleteOrder(ActiveOrder order)
        {
            var card = orderCards.Find(c => c.GetActiveOrder() == order);
            if (card != null)
            {
                OnOrderCardCompleted(card);
            }
        }
    }
}
