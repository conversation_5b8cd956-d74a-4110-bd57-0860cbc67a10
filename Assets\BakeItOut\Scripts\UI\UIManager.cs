using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BakeItOut.Core;
using BakeItOut.Data;
using System.Collections.Generic;

namespace BakeItOut.UI
{
    public class UIManager : MonoBehaviour
    {
        [Header("Main UI Panels")]
        public GameObject mainHUD;
        public GameObject inventoryPanel;
        public GameObject recipeBookPanel;
        public GameObject orderPanel;
        public GameObject pauseMenu;
        public GameObject technologyTreePanel;
        
        [Header("HUD Elements")]
        public TextMeshProUGUI playerLevelText;
        public Slider experienceBar;
        public TextMeshProUGUI moneyText;
        public TextMeshProUGUI gameTimeText;
        
        [Header("Order Display")]
        public Transform orderListParent;
        public GameObject orderItemPrefab;
        
        [Header("Inventory Display")]
        public Transform inventoryGridParent;
        public GameObject inventorySlotPrefab;
        
        [Header("Recipe Book")]
        public Transform recipeListParent;
        public GameObject recipeItemPrefab;
        
        // UI State
        private bool isInventoryOpen = false;
        private bool isRecipeBookOpen = false;
        private bool isPauseMenuOpen = false;
        
        // UI Element Lists
        private List<GameObject> activeOrderItems = new List<GameObject>();
        private List<GameObject> inventorySlots = new List<GameObject>();
        private List<GameObject> recipeItems = new List<GameObject>();
        
        private void Start()
        {
            InitializeUI();
            SubscribeToEvents();
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }
        
        private void InitializeUI()
        {
            // Initialize all panels as closed except main HUD
            if (mainHUD) mainHUD.SetActive(true);
            if (inventoryPanel) inventoryPanel.SetActive(false);
            if (recipeBookPanel) recipeBookPanel.SetActive(false);
            if (orderPanel) orderPanel.SetActive(true);
            if (pauseMenu) pauseMenu.SetActive(false);
            if (technologyTreePanel) technologyTreePanel.SetActive(false);
            
            // Initialize UI elements
            UpdatePlayerStats();
            RefreshOrderDisplay();
            RefreshInventoryDisplay();
            RefreshRecipeBook();
        }
        
        private void SubscribeToEvents()
        {
            if (GameManager.Instance != null)
            {
                var playerManager = GameManager.Instance.PlayerManager;
                var orderManager = GameManager.Instance.OrderManager;
                var inventoryManager = GameManager.Instance.InventoryManager;
                
                // Player events
                if (playerManager != null)
                {
                    playerManager.OnLevelUp += OnPlayerLevelUp;
                    playerManager.OnExperienceGained += OnExperienceGained;
                    playerManager.OnMoneyChanged += OnMoneyChanged;
                    playerManager.OnRecipeUnlocked += OnRecipeUnlocked;
                }
                
                // Order events
                if (orderManager != null)
                {
                    orderManager.OnOrderReceived += OnOrderReceived;
                    orderManager.OnOrderCompleted += OnOrderCompleted;
                    orderManager.OnOrderExpired += OnOrderExpired;
                }
                
                // Inventory events
                if (inventoryManager != null)
                {
                    inventoryManager.OnInventoryChanged += RefreshInventoryDisplay;
                }
                
                // Game events
                GameManager.Instance.OnGameTimeUpdated += OnGameTimeUpdated;
                GameManager.Instance.OnPauseStateChanged += OnPauseStateChanged;
            }
        }
        
        private void UnsubscribeFromEvents()
        {
            if (GameManager.Instance != null)
            {
                var playerManager = GameManager.Instance.PlayerManager;
                var orderManager = GameManager.Instance.OrderManager;
                var inventoryManager = GameManager.Instance.InventoryManager;
                
                if (playerManager != null)
                {
                    playerManager.OnLevelUp -= OnPlayerLevelUp;
                    playerManager.OnExperienceGained -= OnExperienceGained;
                    playerManager.OnMoneyChanged -= OnMoneyChanged;
                    playerManager.OnRecipeUnlocked -= OnRecipeUnlocked;
                }
                
                if (orderManager != null)
                {
                    orderManager.OnOrderReceived -= OnOrderReceived;
                    orderManager.OnOrderCompleted -= OnOrderCompleted;
                    orderManager.OnOrderExpired -= OnOrderExpired;
                }
                
                if (inventoryManager != null)
                {
                    inventoryManager.OnInventoryChanged -= RefreshInventoryDisplay;
                }
                
                GameManager.Instance.OnGameTimeUpdated -= OnGameTimeUpdated;
                GameManager.Instance.OnPauseStateChanged -= OnPauseStateChanged;
            }
        }
        
        // Input handling methods
        public void ToggleInventory()
        {
            isInventoryOpen = !isInventoryOpen;
            if (inventoryPanel) inventoryPanel.SetActive(isInventoryOpen);
            
            if (isInventoryOpen)
            {
                RefreshInventoryDisplay();
            }
        }
        
        public void ToggleRecipeBook()
        {
            isRecipeBookOpen = !isRecipeBookOpen;
            if (recipeBookPanel) recipeBookPanel.SetActive(isRecipeBookOpen);
            
            if (isRecipeBookOpen)
            {
                RefreshRecipeBook();
            }
        }
        
        public void TogglePauseMenu()
        {
            isPauseMenuOpen = !isPauseMenuOpen;
            if (pauseMenu) pauseMenu.SetActive(isPauseMenuOpen);
            
            if (isPauseMenuOpen)
            {
                GameManager.Instance.PauseGame();
            }
            else
            {
                GameManager.Instance.ResumeGame();
            }
        }
        
        // Event handlers
        private void OnPlayerLevelUp(int newLevel)
        {
            UpdatePlayerStats();
            // Could add level up animation/notification here
        }
        
        private void OnExperienceGained(int amount)
        {
            UpdatePlayerStats();
            // Could add experience gain animation here
        }
        
        private void OnMoneyChanged(float newAmount)
        {
            UpdateMoneyDisplay();
        }
        
        private void OnRecipeUnlocked(RecipeData recipe)
        {
            RefreshRecipeBook();
            // Could add recipe unlock notification here
        }
        
        private void OnOrderReceived(ActiveOrder order)
        {
            RefreshOrderDisplay();
        }
        
        private void OnOrderCompleted(ActiveOrder order)
        {
            RefreshOrderDisplay();
            // Could add order completion animation here
        }
        
        private void OnOrderExpired(ActiveOrder order)
        {
            RefreshOrderDisplay();
            // Could add order expiration notification here
        }
        
        private void OnGameTimeUpdated(float gameTime)
        {
            UpdateGameTimeDisplay(gameTime);
        }
        
        private void OnPauseStateChanged(bool isPaused)
        {
            // Handle pause state UI changes
        }
        
        // UI Update methods
        private void UpdatePlayerStats()
        {
            if (GameManager.Instance?.PlayerManager == null) return;
            
            var playerManager = GameManager.Instance.PlayerManager;
            
            if (playerLevelText)
                playerLevelText.text = $"Level {playerManager.currentLevel}";
            
            if (experienceBar)
            {
                experienceBar.value = playerManager.GetLevelProgress();
            }
            
            UpdateMoneyDisplay();
        }
        
        private void UpdateMoneyDisplay()
        {
            if (GameManager.Instance?.PlayerManager == null) return;
            
            if (moneyText)
                moneyText.text = $"${GameManager.Instance.PlayerManager.currentMoney:F2}";
        }
        
        private void UpdateGameTimeDisplay(float gameTime)
        {
            if (gameTimeText)
            {
                int minutes = Mathf.FloorToInt(gameTime / 60f);
                int seconds = Mathf.FloorToInt(gameTime % 60f);
                gameTimeText.text = $"{minutes:00}:{seconds:00}";
            }
        }
        
        private void RefreshOrderDisplay()
        {
            // Clear existing order items
            foreach (var item in activeOrderItems)
            {
                if (item) Destroy(item);
            }
            activeOrderItems.Clear();
            
            // Add current orders
            if (GameManager.Instance?.OrderManager != null && orderListParent && orderItemPrefab)
            {
                foreach (var order in GameManager.Instance.OrderManager.activeOrders)
                {
                    GameObject orderItem = Instantiate(orderItemPrefab, orderListParent);
                    // Configure order item UI here
                    activeOrderItems.Add(orderItem);
                }
            }
        }
        
        private void RefreshInventoryDisplay()
        {
            // Implementation for inventory display refresh
            // This would populate the inventory grid with current items
        }
        
        private void RefreshRecipeBook()
        {
            // Implementation for recipe book refresh
            // This would show available and unlocked recipes
        }
    }
}
