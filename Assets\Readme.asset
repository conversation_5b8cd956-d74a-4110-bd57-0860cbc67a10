%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcf7219bab7fe46a1ad266029b2fee19, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  icon: {fileID: 2800000, guid: d19680cd422524695938fbe55cc3b3bd, type: 3}
  title: HDRP Empty Template
  sections:
  - heading: Welcome to the HDRP Empty Template
    text: This template includes the settings and assets you need to start creating
      with HDRP.
    linkText: 
    url: 
  - heading: Documentation
    text: 
    linkText: Read more about the HDRP
    url: https://docs.unity3d.com/Packages/com.unity.render-pipelines.high-definition@latest/index.html
  - heading: Samples
    text: 
    linkText: Samples and content you can use in your project
    url: https://docs.unity3d.com/Packages/com.unity.render-pipelines.high-definition@latest?subfolder=/manual/HDRP-Sample-Content.html
  - heading: Forums
    text: 
    linkText: Get answers and support
    url: https://forum.unity.com/forums/high-definition-render-pipeline.386/
  - heading: Bugs
    text: 
    linkText: Report any bugs
    url: https://unity3d.com/unity/qa/bug-reporting
  loadedLayout: 1
