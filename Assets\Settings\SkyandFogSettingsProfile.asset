%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7724654706381055090
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d877ec3e844f2ca46830012e8e79319b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  active: 1
  rotation:
    m_OverrideState: 0
    m_Value: 0
  skyIntensityMode:
    m_OverrideState: 0
    m_Value: 0
  exposure:
    m_OverrideState: 0
    m_Value: 0
  multiplier:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxValue:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxColor:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  type:
    m_OverrideState: 0
    m_Value: 1
  atmosphericScattering:
    m_OverrideState: 0
    m_Value: 1
  renderingMode:
    m_OverrideState: 0
    m_Value: 0
  material:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  airDensityR:
    m_OverrideState: 0
    m_Value: 0.04534
  airDensityG:
    m_OverrideState: 0
    m_Value: 0.10237241
  airDensityB:
    m_OverrideState: 0
    m_Value: 0.23264056
  airTint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  airMaximumAltitude:
    m_OverrideState: 0
    m_Value: 55261.973
  aerosolDensity:
    m_OverrideState: 0
    m_Value: 0.01192826
  aerosolTint:
    m_OverrideState: 0
    m_Value: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  aerosolMaximumAltitude:
    m_OverrideState: 0
    m_Value: 8289.296
  aerosolAnisotropy:
    m_OverrideState: 0
    m_Value: 0
  ozoneDensityDimmer:
    m_OverrideState: 0
    m_Value: 1
  ozoneMinimumAltitude:
    m_OverrideState: 0
    m_Value: 20000
  ozoneLayerWidth:
    m_OverrideState: 0
    m_Value: 20000
  groundTint:
    m_OverrideState: 1
    m_Value: {r: 0.122641504, g: 0.1043775, b: 0.09313812, a: 1}
  groundColorTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  groundEmissionTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  groundEmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  planetRotation:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  spaceEmissionTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  spaceEmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  spaceRotation:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  colorSaturation:
    m_OverrideState: 0
    m_Value: 1
  alphaSaturation:
    m_OverrideState: 0
    m_Value: 1
  alphaMultiplier:
    m_OverrideState: 0
    m_Value: 1
  horizonTint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  zenithTint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  horizonZenithShift:
    m_OverrideState: 0
    m_Value: 0
  m_SkyVersion: 2
  m_ObsoleteEarthPreset:
    m_OverrideState: 0
    m_Value: 1
  planetaryRadius:
    m_OverrideState: 0
    m_Value: 6378100
  planetCenterPosition:
    m_OverrideState: 0
    m_Value: {x: 0, y: -6378100, z: 0}
--- !u!114 &-4151792930034644520
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 1
  enabled:
    m_OverrideState: 1
    m_Value: 1
  colorMode:
    m_OverrideState: 0
    m_Value: 1
  color:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
  baseHeight:
    m_OverrideState: 0
    m_Value: 0
  maximumHeight:
    m_OverrideState: 0
    m_Value: 50
  meanFreePath:
    m_OverrideState: 0
    m_Value: 400
  enableVolumetricFog:
    m_OverrideState: 1
    m_Value: 1
  albedo:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  globalLightProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
  depthExtent:
    m_OverrideState: 0
    m_Value: 64
  denoisingMode:
    m_OverrideState: 0
    m_Value: 2
  anisotropy:
    m_OverrideState: 1
    m_Value: 0.65
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
  multipleScatteringIntensity:
    m_OverrideState: 0
    m_Value: 0
  m_FogControlMode:
    m_OverrideState: 0
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 0
    m_Value: 12.5
  volumeSliceCount:
    m_OverrideState: 0
    m_Value: 64
  m_VolumetricFogBudget:
    m_OverrideState: 0
    m_Value: 0.5
  m_ResolutionDepthRatio:
    m_OverrideState: 0
    m_Value: 0.5
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: SkyandFogSettingsProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 1142777632297148762}
  - {fileID: -7724654706381055090}
  - {fileID: -4151792930034644520}
  - {fileID: 7642060734654139733}
--- !u!114 &1142777632297148762
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  active: 1
  skyType:
    m_OverrideState: 1
    m_Value: 4
  cloudType:
    m_OverrideState: 0
    m_Value: 0
  skyAmbientMode:
    m_OverrideState: 1
    m_Value: 0
  planetRadius:
    m_OverrideState: 0
    m_Value: 6378.1
  renderingSpace:
    m_OverrideState: 0
    m_Value: 1
  centerMode:
    m_OverrideState: 0
    m_Value: 0
  planetCenter:
    m_OverrideState: 0
    m_Value: {x: 0, y: -6378.1, z: 0}
  windOrientation:
    m_OverrideState: 0
    m_Value: 0
  windSpeed:
    m_OverrideState: 0
    m_Value: 0
  fogType:
    m_OverrideState: 1
    m_Value: 0
  m_Version: 1
--- !u!114 &7642060734654139733
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 4
  meteringMode:
    m_OverrideState: 0
    m_Value: 2
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 0
    m_Value: 0
  compensation:
    m_OverrideState: 0
    m_Value: 0
  limitMin:
    m_OverrideState: 1
    m_Value: 2
  limitMax:
    m_OverrideState: 1
    m_Value: 14
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 0
    m_Value: {x: 40, y: 90}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 0
    m_Value: {x: 0.15, y: 0.15}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 0
    m_Value: 0.5
