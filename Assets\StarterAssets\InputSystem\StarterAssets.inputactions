{"name": "StarterAssets", "maps": [{"name": "Player", "id": "f62a4b92-ef5e-4175-8f4c-c9075429d32c", "actions": [{"name": "Move", "type": "Value", "id": "6bc1aaf4-b110-4ff7-891e-5b9fe6f32c4d", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "Look", "type": "Value", "id": "2690c379-f54d-45be-a724-414123833eb4", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "8c4abdf8-4099-493a-aa1a-129acec7c3df", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "Sprint", "type": "PassThrough", "id": "980e881e-182c-404c-8cbf-3d09fdb48fef", "expectedControlType": "", "processors": "", "interactions": ""}], "bindings": [{"name": "WASD", "id": "b7594ddb-26c9-4ba2-bd5a-901468929edc", "path": "2DVector(mode=1)", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "2063a8b5-6a45-43de-851b-65f3d46e7b58", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "64e4d037-32e1-4fb9-80e4-fc7330404dfe", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "0fce8b11-5eab-4e4e-a741-b732e7b20873", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "7bdda0d6-57a8-47c8-8238-8aecf3110e47", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "bb94b405-58d3-4998-8535-d705c1218a98", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "929d9071-7dd0-4368-9743-6793bb98087e", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "28abadba-06ff-4d37-bb70-af2f1e35a3b9", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "45f115b6-9b4f-4ba8-b500-b94c93bf7d7e", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "e2f9aa65-db06-4c5b-a2e9-41bc8acb9517", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "StickDeadzone", "groups": "Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ed66cbff-2900-4a62-8896-696503cfcd31", "path": "<Pointer>/delta", "interactions": "", "processors": "InvertVector2(invertX=false),ScaleVector2(x=0.05,y=0.05)", "groups": "KeyboardMouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d1d171b6-19d8-47a6-ba3a-71b6a8e7b3c0", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "InvertVector2(invertX=false),StickDeadzone,ScaleVector2(x=300,y=300)", "groups": "Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1bd55a0b-761e-4ae4-89ae-8ec127e08a29", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9f973413-5e27-4239-acee-38c4a63feeba", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "dc65b89f-9bd3-43fb-92af-d0d87ba5faa4", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c8fcd86e-dcfd-4f88-8e93-b638cdbf3320", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "KeyboardMouse", "bindingGroup": "KeyboardMouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": true, "isOR": false}, {"devicePath": "<XInputController>", "isOptional": true, "isOR": false}, {"devicePath": "<DualShockGamepad>", "isOptional": true, "isOR": false}]}, {"name": "Xbox Controller", "bindingGroup": "Xbox Controller", "devices": []}, {"name": "PS4 Controller", "bindingGroup": "PS4 Controller", "devices": []}]}