%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcf7219bab7fe46a1ad266029b2fee19, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  icon: {fileID: 2800000, guid: 44a17a5b205fcd349a24700e6f5615bd, type: 3}
  title: Welcome to Starter Assets!
  sections:
  - heading: Starter Assets Readme
    text: 
    linkText: 
    url: 
  - heading: 
    text: 'Starter Assets include First and Third Person Character controllers built
      in a modular way as a solid foundation for any game genre. '
    linkText: Starter Assets - First Person Character Controller
    url: http://u3d.as/2z1q
  - heading: 
    text: 
    linkText: Starter Assets - Third Person Character Controller
    url: http://u3d.as/2z1r
  - heading: 
    text: "The Starter Assets packages are compatible with Unity 2020.3 LTS and leverage
      Unity\u2019s CharacterController component."
    linkText: 
    url: 
  - heading: Important note
    text: The Starter Assets packages require the Input System and Cinemachine packages
      to work. See the documentation (Assets/StarterAssets/StarterAssets_Documentation.pdf)
      for more information.
    linkText: 
    url: 
  - heading: 'Get started  '
    text: In StarterAssets/ThirdPersonController/Scenes or StarterAssets/FirstPersonController/Scenes,
      you will find the Playground Scene. Here you can use the Starter Assets controller
      in a simple playground environment and start adjusting the controller settings
      to your liking.
    linkText: 
    url: 
  - heading: Documentation
    text: See the documentation (Assets/StarterAssets/StarterAssets_Documentation.pdf)
      for more information on how to set up the Starter Assets in a new Scene, in
      HDRP/URP, or for mobile devices.
    linkText: 
    url: 
  - heading: 'We want to hear your feedback! '
    text: 
    linkText: Click here to let us know what you think about this package.
    url: https://unitysoftware.co1.qualtrics.com/jfe/form/SV_5cg7IkyOprbHqia?packagename=starterassets
  loadedLayout: 1
