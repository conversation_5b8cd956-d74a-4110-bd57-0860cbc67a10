-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.ref.dll"
-define:UNITY_6000_1_12
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:HDRP_1_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.package-validation-suite@536239bd7458/Lib/Editor/log4net.dll"
-r:"Library/PackageCache/com.unity.package-validation-suite@536239bd7458/Lib/Editor/Unity.APIComparison.Framework.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Analytics/AssetReimporterAnalytic.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/AssetVersion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/AutodeskInteractiveMaterialImport.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/CubeLutImporter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/FBXMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/HDIESImporterEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/HDRenderPipelineGlobalSettingsPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/MaterialPostProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/ModelPostProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/NormalMapFilteringTexturePostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/PhysicalMaterial3DsMaxPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/PluginMaterialVersions.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/ShaderGraphMaterialsUpdater.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/SketchupMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/AssetProcessors/ThreeDSMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/HDRPBuildData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/HDRPBuildDataValidator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/HDRPPreprocessBuild.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/HDRPPreprocessShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/SettingsStrippers/HDRPRayTracingResourcesStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/ShaderStrippers/HDRPComputeShaderVariantStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/ShaderStrippers/HDRPDisabledComputeShaderVariantStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/BuildProcessors/ShaderStrippers/HDRPShaderVariantStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/CompositionFilterUI.Drawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/CompositionLayerUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/CompositionManagerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/CompositionManagerEditor.Styles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/CompositionUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/CompositorWindow.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/SerializedCompositionFilter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/SerializedCompositionLayer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/SerializedCompositionManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/SeriallizedShaderProperty.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Compositor/ShaderPropertyUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/ContextualMenuDispatcher.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Core/HDRenderPipelinePreferencesProvider.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Core/TextureCombiner/TextureCombiner.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HDAnalytics.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslParser.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslTokenizer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslTree.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslUnityReserved.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/HlslUtil.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/PartialDerivUtilWriter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/HlslDerivatives/ShaderTokenUtil.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/AdditionalShadowDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/DiffusionProfileListEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDAdditionalLightDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDIESImporter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightExplorerExtension.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightUI.ContextualMenu.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightUI.Handles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightUI.PresetInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/HDLightUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/IndirectLightingControllerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/LightmappingHDRP.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/LightUnit/HDLightUnitSliderUIDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/LightUnit/HDPiecewiseLightUnitSlider.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/CameraSettingsUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDAdditionalReflectionDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDBakedReflectionSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDProbeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDProbeUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDProbeUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDProbeUI.Handles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDProbeUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDReflectionProbeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDReflectionProbeEditor.Gizmos.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDReflectionProbeEditor.Preview.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDReflectionProbeEditor.ProbeUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/HDScreenSpaceReflectionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/PlanarReflectionProbeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/ProbeSettingsUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/ReflectionMenuItem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/ScreenSpaceRefractionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/SerializedCameraSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/SerializedHDProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/SerializedHDReflectionProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/SerializedPlanarReflectionProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/SerializedProbeSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Gizmos.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Handles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/ProxyVolumeUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/ReflectionProxyVolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/SerializedInfluenceVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/SerializedProxyVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Reflection/Volume/SerializedReflectionProxyVolumeComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/RenderingLayerMaskPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/ScreenSpaceAmbientOcclusionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/SerializedHDLight.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Shadow/ContactShadowsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Shadow/HDShadowSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/Shadow/MicroShadowingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricClouds/VolumetricCloudsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricClouds/VolumetricCloudsResourcesStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricClouds/WorleyfBmGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricLighting/LocalVolumetricFogEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricLighting/LocalVolumetricFogUI.Drawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricLighting/LocalVolumetricFogUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricLighting/SerializedLocalVolumetricFog.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Lighting/VolumetricLighting/VolumetricMenuItem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/AxF/AxFGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/BaseShaderPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Canvas/ShaderGraph/CreateHDCanvasShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Canvas/ShaderGraph/HDCanvasData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Canvas/ShaderGraph/HDCanvasSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/DecalMenuItems.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/DecalProjectorEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/DecalProjectorEditor.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/DecalUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/DisplacableRectHandles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ProjectedTransform.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ShaderGraph/CreateDecalShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ShaderGraph/DecalData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ShaderGraph/DecalPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ShaderGraph/DecalShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ShaderGraph/DecalSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Decal/ShaderGraph/DecalSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/DiffusionProfile/DiffusionProfileMaterialUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/DiffusionProfile/DiffusionProfileSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/DiffusionProfile/DiffusionProfileSettingsEditor.Styles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/DiffusionProfile/SerializedDiffusionProfileSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/CreateEyeShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/EyeData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/EyeSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/EyeSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/EyeSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/BuiltinCorneaIOR.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/BuiltinIrisPlaneOffset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/BuiltinIrisRadius.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/CirclePupilAnimation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/CorneaRefraction.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/EyeSurfaceTypeDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/IrisLimbalRing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/IrisOffset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/IrisOutOfBoundColorClamp.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/IrisUVLocation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/ScleraIrisBlend.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/ScleraLimbalRing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Eye/ShaderGraph/Node/ScleraUVLocation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fabric/ShaderGraph/CreateFabricShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fabric/ShaderGraph/FabricData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fabric/ShaderGraph/FabricSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fabric/ShaderGraph/FabricSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fabric/ShaderGraph/FabricSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/FogVolume/FogVolumePropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/FogVolume/FogVolumeShaderGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/FogVolume/ShaderGraph/CreateFogVolumeShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/FogVolume/ShaderGraph/FogVolumeData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/FogVolume/ShaderGraph/FogVolumeSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/FogVolume/ShaderGraph/FogVolumeUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fullscreen/ShaderGraph/CreateHDFullscreenShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fullscreen/ShaderGraph/HDFullscreenData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Fullscreen/ShaderGraph/HDFullscreenSubtarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Hair/ShaderGraph/CreateHairShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Hair/ShaderGraph/HairData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Hair/ShaderGraph/HairPropertyBlocks.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Hair/ShaderGraph/HairSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Hair/ShaderGraph/HairSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/LayeredLit/LayeredLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/LitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/LitShaderPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/ShaderGraph/CreateHDLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/ShaderGraph/HDLitData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/ShaderGraph/HDLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/ShaderGraph/HDLitSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/ShaderGraph/LitSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Lit/StandardsToHDLitMaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/LTCAreaLight/LTC.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/LTCAreaLight/LTCTableGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/LTCAreaLight/LTCTableGeneratorEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/LTCAreaLight/NelderMead.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Nature/HDSpeedTree8MaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Nature/HDSpeedTree9MaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/PBRSky/ShaderGraph/PBRSkySubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/AdvancedOptionsPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/DiffusionProfileMaterialPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/DiffusionProfilePropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/DiffusionProfileShaderProperty.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/DistortionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDBlockFields.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDFields.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDMetadata.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDPropertiesHeader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDShaderKernels.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDShaderPasses.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDStructFields.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDStructs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDSubShaderUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/HDTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/IPluginSubTargetMaterialUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/DecalMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/EyeMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/FabricMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/HairMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/HDLitMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/HDUnlitMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Legacy/StackLitMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/LightingSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/CustomPassNodes.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/DiffusionProfileNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/EmissionNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/ExposureNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/FresnelEquationNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/HDSampleBufferNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/HDSceneColorNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/HDSceneDepthNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/RayTracingQualityNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Nodes/SurfaceGradientResolveNormal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/PassDescriptorExtension.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/ShaderGraphVersion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Slots/DefaultMaterialSlot.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Slots/DiffusionProfileInputMaterialSlot.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/SubTargetPropertiesGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/SubTargetPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/SurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/SurfaceSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/TargetData/BuiltinData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/TargetData/HDTargetData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/TargetData/LightingData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/TargetData/SystemData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/ShaderGraph/Views/DiffusionProfileSlotControlView.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/SixWayLit/ShaderGraph/CreateSixWayShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/SixWayLit/ShaderGraph/HDSixWayData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/SixWayLit/ShaderGraph/HDSixWaySubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/SixWayLit/ShaderGraph/SixWaySurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/SixWayLit/SixWayGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/StackLit/ShaderGraph/CreateStackLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/StackLit/ShaderGraph/StackLitAdvancedOptionsPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/StackLit/ShaderGraph/StackLitData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/StackLit/ShaderGraph/StackLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/StackLit/ShaderGraph/StackLitSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/StackLit/ShaderGraph/StackLitSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/TerrainLit/StandardsTerrainToHDTerrainLitUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/TerrainLit/TerrainLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/AdvancedOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/AxfMainSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/AxfSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/DecalSortingInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/DecalSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/DecalSurfaceOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/DetailInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/DistortionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/EmissionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/HDShaderGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/LayeringOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/LayerListUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/LayersUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/LightingShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/LitShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/LitSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/MaterialUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/MaterialUIBlockList.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/RefractionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/ShaderGraphUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/SixWayUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/SurfaceOptionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/TessellationOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/TransparencyUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/UIBlocks/UnlitSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/CreateHDUnlitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/HDUnlitData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/HDUnlitDistortionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/HDUnlitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/HDUnlitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/HDUnlitSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/HDUnlitSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/ShaderGraph/UnlitShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/UnlitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Unlit/UnlitsToHDUnlitUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/CreateWaterShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/BlendNormal_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/ComputeVertexPosition_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateDisplacement_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateFoamData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateRefractionData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateScatteringColor_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateSimulationAdditionalData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateSimulationCaustics_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/EvaluateTipThickness_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/GetCameraHeightFromWater.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/PackVertexData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/Node/UnpackData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/WaterData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/WaterDecalSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/WaterSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/WaterSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Material/Water/ShaderGraph/WaterSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PackageInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/BloomEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/ChromaticAberrationEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/ColorCurvesEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/CustomPostProcessVolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/DepthOfFieldEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/ExposureEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/FilmGrainEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/LiftGammaGainEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/MotionBlurEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/PaniniProjectionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/ScreenSpaceLensFlareEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/SerializedGlobalPostProcessSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/ShadowsMidtonesHighlightsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/TonemappingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/TrackballUIDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PostProcessing/VignetteEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PropertyDrawers/CustomPostProcessOrdersSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PropertyDrawers/CustomPostProcessVolumeComponentListPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PropertyDrawers/FrameSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PropertyDrawers/HDRPDefaultVolumeProfileSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PropertyDrawers/LookDevVolumeProfileSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/PropertyDrawers/RenderingPathFrameSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RayTracing/ReflectionKernelGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RayTracing/SolidAngleKernelGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/BaseUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDAdditionalCameraEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraEditor.Handlers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraPreview.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.ContextualMenu.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.Environment.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.EnvironmentSkin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.Output.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.Output.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.PhysicalCamera.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.PhysicalCamera.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.PresetInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.Rendering.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/HDCameraUI.Rendering.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Camera/SerializedHDCamera.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/CustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/CustomPassDrawerAttribute.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/CustomPassListSearchWindow.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/CustomPassVolumeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/CustomPassVolumeGizmoDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/DrawRenderersCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/FullScreenCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/ObjectIDCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/CustomPass/VrsCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDAdditionalMeshRendererSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDAdditionalMeshRendererSettingsUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDAssetFactory.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDEditorCLI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDEditorUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDLightUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDRenderPipelineEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDRenderPipelineMenuItems.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDRenderPipelineUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDRenderPipelineUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/HDShaderUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/IUpdateable.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/LineRendering/HDRenderPipeline.LineRendering.VolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/LineRendering/HDShaderPasses.LineRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/PathTracing/PathTracingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Raytracing/GlobalIlluminationEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Raytracing/LightClusterEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Raytracing/RayTracingSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Raytracing/RayTracingShaderPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Raytracing/RecursiveRenderingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Raytracing/SubSurfaceScatteringEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/ScalableSettingLevelParameterEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/DiffusionProfileSettingsListUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/FrameSettingsExtractedDatas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/FrameSettingsUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/FrameSettingsUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/HDRenderingLayersLimitSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/QualitySettingsPanel.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/RenderPipelineSettingsUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedDynamicResolutionSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedFrameSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedGlobalDecalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedGlobalLightLoopSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedGPUResidentDrawerSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedHDRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedHDShadowInitParameters.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedLightingQualitySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedLowResTransparencySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedPostProcessingQualitySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedRenderPipelineSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedScalableSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedScalableSettingValue.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedVirtualTexturingSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/Settings/SerializedXRSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/TargetMidGrayParameterDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/VirtualTexturingSettingsUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipeline/VolumeComponentWithQualityEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/RenderPipelineResources/HDProjectSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/SceneTemplates/HDRPBasicScenePipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/AtmosphericScattering/FogEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/CloudSystem/CloudLayer/CloudLayerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/GradientSky/GradientSkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/HDLightingWindowEnvironmentSection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/HDRISky/HDRISkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/PhysicallyBasedSky/PhysicallyBasedSkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/SkySettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/StaticLightingSkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Sky/VisualEnvironmentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Tools/ColorCheckerToolEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Upgraders/UpgradeStandardShaderMaterials.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXAbstractDistortionOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXAbstractParticleHDRPLitOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXAbstractParticleHDRPOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXDecalHDRPOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXDistortionMeshOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXDistortionPlanarPrimitiveOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXDistortionQuadStripOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXLitCubeOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXLitMeshOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXLitPlanarPrimitiveOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXLitQuadStripOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXLitSphereOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Outputs/VFXVolumetricFogOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/UIBlocks/VFXShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/Utility/VFXHDRPSettingsUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/VFXHDRPBinder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/VFXGraph/VFXHDRPSubOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterDecal/WaterDecalEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterDecal/WaterDecalShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterDecal/WaterDecalSurfaceOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterExcluder/WaterExcluderEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterRenderingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/VFX/SampleWaterSurface.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.Appearance.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.Decal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.Foam.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.Miscellaneous.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.Simulation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurface/WaterSurfaceEditor.Tooltips.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSurfaceMenuItems.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Water/WaterSystemResourcesStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Wizard/HDWizard.Configuration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Wizard/HDWizard.ProjectSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Wizard/HDWizard.UIElement.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Wizard/HDWizard.UserSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@5fc994029d91/Editor/Wizard/HDWizard.Window.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.UnityAdditionalFile.txt"