# About Netcode for GameObjects

Netcode for GameObjects is a Unity package that provides networking capabilities to GameObject & MonoBehaviour workflows.

## Guides

See guides below to install Unity Netcode for GameObjects, set up your project, and get started with your first networked game:

- [Documentation](https://docs-multiplayer.unity3d.com/netcode/current/about)
- [Installation](https://docs-multiplayer.unity3d.com/netcode/current/installation)
- [First Steps](https://docs-multiplayer.unity3d.com/netcode/current/tutorials/get-started-ngo)
- [API Reference](https://docs.unity3d.com/Packages/com.unity.netcode.gameobjects@1.6/api/index.html)

# Technical details

## Requirements

Netcode for GameObjects targets the following Unity versions:
- Unity 2021.3 (LTS), 2022.3 (LTS) and 2023.2

On the following runtime platforms:
- Windows, MacOS, and Linux
- iOS and Android
- Most closed platforms, such as consoles. Contact us for more information about specific closed platforms.

## Document revision history

|Date|Reason|
|---|---|
|March 10, 2021|Document created. Matches package version 0.1.0|
|June 1, 2021|Update and add links for additional content. Matches patch version 0.1.0 and hotfixes.|
|June 3, 2021|Update document to acknowledge Unity min version change. Matches package version 0.2.0|
|August 5, 2021|Update product/package name|
|September 9,2021|Updated the links and name of the file.|
|April 20, 2022|Updated links|
