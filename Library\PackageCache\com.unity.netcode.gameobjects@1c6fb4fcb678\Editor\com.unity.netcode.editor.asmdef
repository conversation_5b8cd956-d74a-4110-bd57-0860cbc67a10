{"name": "Unity.Netcode.Editor", "rootNamespace": "Unity.Netcode.Editor", "references": ["Unity.Netcode.Runtime", "Unity.Netcode.Components", "Unity.Services.Relay", "Unity.Networking.Transport", "Unity.Services.Core", "Unity.Services.Authentication"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.multiplayer.tools", "expression": "", "define": "MULTIPLAYER_TOOLS"}, {"name": "Unity", "expression": "(0,2022.2.0a5)", "define": "UNITY_UNET_PRESENT"}, {"name": "com.unity.modules.animation", "expression": "", "define": "COM_UNITY_MODULES_ANIMATION"}, {"name": "com.unity.modules.physics", "expression": "", "define": "COM_UNITY_MODULES_PHYSICS"}, {"name": "com.unity.modules.physics2d", "expression": "", "define": "COM_UNITY_MODULES_PHYSICS2D"}, {"name": "com.unity.services.relay", "expression": "1.0", "define": "RELAY_SDK_INSTALLED"}, {"name": "com.unity.transport", "expression": "2.0", "define": "UTP_TRANSPORT_2_0_ABOVE"}, {"name": "com.unity.services.multiplayer", "expression": "0.2.0", "define": "MULTIPLAYER_SERVICES_SDK_INSTALLED"}], "noEngineReferences": false}