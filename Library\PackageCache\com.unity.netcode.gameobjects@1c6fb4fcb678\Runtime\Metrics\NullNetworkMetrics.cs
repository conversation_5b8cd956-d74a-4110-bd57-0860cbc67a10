using System.Collections.Generic;

namespace Unity.Netcode
{
    internal class NullNetworkMetrics : INetworkMetrics
    {
        public void SetConnectionId(ulong connectionId)
        {
        }

        public void TrackTransportBytesSent(long bytesCount)
        {
        }

        public void TrackTransportBytesReceived(long bytesCount)
        {
        }

        public void TrackNetworkMessageSent(ulong receivedClientId, string messageType, long bytesCount)
        {
        }

        public void TrackNetworkMessageReceived(ulong senderClientId, string messageType, long bytesCount)
        {
        }

        public void TrackNamedMessageSent(ulong receiverClientId, string messageName, long bytesCount)
        {
        }

        public void TrackNamedMessageSent(IReadOnlyCollection<ulong> receiverClientIds, string messageName, long bytesCount)
        {
        }

        public void TrackNamedMessageReceived(ulong senderClientId, string messageName, long bytesCount)
        {
        }

        public void TrackUnnamedMessageSent(ulong receiverClientId, long bytesCount)
        {
        }

        public void TrackUnnamedMessageSent(IReadOnlyCollection<ulong> receiverClientIds, long bytesCount)
        {
        }

        public void TrackUnnamedMessageReceived(ulong senderClientId, long bytesCount)
        {
        }

        public void TrackNetworkVariableDeltaSent(
            ulong receiverClientId,
            NetworkObject networkObject,
            string variableName,
            string networkBehaviourName,
            long bytesCount)
        {
        }

        public void TrackNetworkVariableDeltaReceived(
            ulong senderClientId,
            NetworkObject networkObject,
            string variableName,
            string networkBehaviourName,
            long bytesCount)
        {
        }

        public void TrackOwnershipChangeSent(ulong receiverClientId, NetworkObject networkObject, long bytesCount)
        {
        }

        public void TrackOwnershipChangeReceived(ulong senderClientId, NetworkObject networkObject, long bytesCount)
        {
        }

        public void TrackObjectSpawnSent(ulong receiverClientId, NetworkObject networkObject, long bytesCount)
        {
        }

        public void TrackObjectSpawnReceived(ulong senderClientId, NetworkObject networkObject, long bytesCount)
        {
        }

        public void TrackObjectDestroySent(ulong senderClientId, NetworkObject networkObject, long bytesCount)
        {
        }

        public void TrackObjectDestroyReceived(ulong senderClientId, NetworkObject networkObject, long bytesCount)
        {
        }

        public void TrackRpcSent(
            ulong receiverClientId,
            NetworkObject networkObject,
            string rpcName,
            string networkBehaviourName,
            long bytesCount)
        {
        }

        public void TrackRpcSent(
            ulong[] receiverClientIds,
            NetworkObject networkObject,
            string rpcName,
            string networkBehaviourName,
            long bytesCount)
        {
        }

        public void TrackRpcReceived(
            ulong senderClientId,
            NetworkObject networkObject,
            string rpcName,
            string networkBehaviourName,
            long bytesCount)
        {
        }

        public void TrackServerLogSent(ulong receiverClientId, uint logType, long bytesCount)
        {
        }

        public void TrackServerLogReceived(ulong senderClientId, uint logType, long bytesCount)
        {
        }

        public void TrackSceneEventSent(IReadOnlyList<ulong> receiverClientIds, uint sceneEventType, string sceneName, long bytesCount)
        {
        }

        public void TrackSceneEventSent(ulong receiverClientId, uint sceneEventType, string sceneName, long bytesCount)
        {
        }

        public void TrackSceneEventReceived(ulong senderClientId, uint sceneEventType, string sceneName, long bytesCount)
        {
        }

        public void TrackPacketSent(uint packetCount)
        {
        }

        public void TrackPacketReceived(uint packetCount)
        {
        }

        public void UpdateRttToServer(int rtt)
        {
        }

        public void UpdateNetworkObjectsCount(int count)
        {
        }

        public void UpdateConnectionsCount(int count)
        {
        }

        public void UpdatePacketLoss(float packetLoss)
        {
        }

        public void DispatchFrame()
        {
        }
    }
}
