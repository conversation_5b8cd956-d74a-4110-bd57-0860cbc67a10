{"name": "Unity.Netcode.Runtime", "rootNamespace": "Unity.Netcode", "references": ["Unity.Multiplayer.MetricTypes", "Unity.Multiplayer.NetStats", "Unity.Multiplayer.NetStatsReporting", "Unity.Multiplayer.NetworkSolutionInterface", "Unity.Multiplayer.Tools.MetricTypes", "Unity.Multiplayer.Tools.NetStats", "Unity.Multiplayer.Tools.NetStatsReporting", "Unity.Multiplayer.Tools.NetworkSolutionInterface", "Unity.Networking.Transport", "Unity.Collections", "Unity.Burst", "Unity.Mathematics"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.multiplayer.tools", "expression": "", "define": "MULTIPLAYER_TOOLS"}, {"name": "Unity", "expression": "(0,2022.2.0a5)", "define": "UNITY_UNET_PRESENT"}, {"name": "com.unity.multiplayer.tools", "expression": "1.0.0-pre.7", "define": "MULTIPLAYER_TOOLS_1_0_0_PRE_7"}, {"name": "com.unity.transport", "expression": "2.0.0-exp", "define": "UTP_TRANSPORT_2_0_ABOVE"}, {"name": "com.unity.transport", "expression": "2.1.0", "define": "UTP_TRANSPORT_2_1_ABOVE"}, {"name": "Unity", "expression": "2023", "define": "UNITY_DEDICATED_SERVER_ARGUMENTS_PRESENT"}, {"name": "com.unity.modules.animation", "expression": "", "define": "COM_UNITY_MODULES_ANIMATION"}, {"name": "com.unity.modules.physics", "expression": "", "define": "COM_UNITY_MODULES_PHYSICS"}, {"name": "com.unity.modules.physics2d", "expression": "", "define": "COM_UNITY_MODULES_PHYSICS2D"}, {"name": "com.unity.services.multiplayer", "expression": "0.2.0", "define": "MULTIPLAYER_SERVICES_SDK_INSTALLED"}, {"name": "Unity", "expression": "6000.0.11f1", "define": "COM_UNITY_MODULES_PHYSICS2D_LINEAR"}], "noEngineReferences": false}