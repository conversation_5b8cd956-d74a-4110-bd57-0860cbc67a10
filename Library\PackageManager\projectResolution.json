{"context": {"projectPath": "C:/Users/<USER>/Desktop/coding stuff/bake it out demo/Packages", "unityVersion": "6000.1.12f1"}, "inputs": ["C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Packages\\manifest.json", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Packages\\packages-lock.json", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.unity.ai.navigation@2.0.8": {"name": "com.unity.ai.navigation", "displayName": "AI Navigation", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.ai.navigation@eb5635ad590d", "fingerprint": "eb5635ad590d47cef2a5c920d9475cc222db3f67", "editorCompatibility": "6000.0.0a1", "version": "2.0.8", "source": "registry", "testable": false}, "com.unity.burst@1.8.23": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.burst@6aff1dd08a0c", "fingerprint": "6aff1dd08a0c2f92ddb7f56ec033a5cb88967056", "editorCompatibility": "2021.3.0a1", "version": "1.8.23", "source": "registry", "testable": false}, "com.unity.cinemachine@2.10.4": {"name": "com.unity.cinemachine", "displayName": "Cinemachine", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.cinemachine@915237edaa40", "fingerprint": "915237edaa40461a941e260ca123faf48eaf6a40", "editorCompatibility": "2022.3.0a1", "version": "2.10.4", "source": "registry", "testable": false}, "com.unity.collab-proxy@2.8.2": {"name": "com.unity.collab-proxy", "displayName": "Version Control", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f", "fingerprint": "c854d1f7d97fbe1905f3e3591ded6fe77d96e654", "editorCompatibility": "2021.3.0f1", "version": "2.8.2", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.ext.nunit@031a54704bff", "fingerprint": "031a54704bffe39e6a0324909f8eaa4565bdebf2", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "builtin", "testable": false}, "com.unity.feature.development@1.0.2": {"name": "com.unity.feature.development", "displayName": "Engineering", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.feature.development@767aadbc6eb7", "fingerprint": "767aadbc6eb72681a4ca807c8fa248e0230a0cef", "version": "1.0.2", "source": "builtin", "testable": false}, "com.unity.ide.rider@3.0.36": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db", "fingerprint": "4d374c7eb6db6907c7e6925e3086c3c73f926e13", "editorCompatibility": "2019.4.6f1", "version": "3.0.36", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.inputsystem@1.14.1": {"name": "com.unity.inputsystem", "displayName": "Input System", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.inputsystem@978211393e28", "fingerprint": "978211393e28699aba338ed157cef347ac565bbf", "editorCompatibility": "2021.3.0a1", "version": "1.14.1", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.multiplayer.center@1.0.0": {"name": "com.unity.multiplayer.center", "displayName": "Multiplayer Center", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546", "fingerprint": "f3fb577b3546594b97b8cc34307cd621f60f1c73", "editorCompatibility": "6000.0.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.netcode.gameobjects@2.1.1": {"name": "com.unity.netcode.gameobjects", "displayName": "Netcode for GameObjects", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.netcode.gameobjects@1c6fb4fcb678", "fingerprint": "1c6fb4fcb67826b9c6dff53c56b94e81ef0079cd", "editorCompatibility": "6000.0.0a1", "version": "2.1.1", "source": "registry", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.package-validation-suite@0.22.0-preview": {"name": "com.unity.package-validation-suite", "displayName": "Package Validation Suite", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.package-validation-suite@536239bd7458", "fingerprint": "536239bd7458d9cf088755abe3972aa68f6ab0d4", "editorCompatibility": "2018.4.0a1", "version": "0.22.0-preview", "source": "registry", "testable": false}, "com.unity.render-pipelines.core@17.1.0": {"name": "com.unity.render-pipelines.core", "displayName": "Scriptable Render Pipeline Core", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.render-pipelines.core@47a0d8baabbd", "fingerprint": "47a0d8baabbd2f9b0addf3f12ab21cbae51f69d5", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.high-definition@17.1.0": {"name": "com.unity.render-pipelines.high-definition", "displayName": "High Definition Render Pipeline", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.render-pipelines.high-definition@5fc994029d91", "fingerprint": "5fc994029d91b177f495e3704cf559412acf453e", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal@17.1.0": {"name": "com.unity.render-pipelines.universal", "displayName": "Universal Render Pipeline", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.render-pipelines.universal@bf6d194af387", "fingerprint": "bf6d194af3878df1faaf2ee899c02de3bba0ed2a", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal-config@17.0.3": {"name": "com.unity.render-pipelines.universal-config", "displayName": "Universal Render Pipeline Config", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.render-pipelines.universal-config@8dc1aab4af1d", "fingerprint": "8dc1aab4af1d718781689a36ed5231a35ad1a524", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.rendering.light-transport@e647573c7d2a", "fingerprint": "e647573c7d2ae78386ecb3f9f962738597f13fcf", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.1.0": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.shadergraph@8d13f365c663", "fingerprint": "8d13f365c6632c6b398ae9cd01d13bcee9880a3b", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.test-framework@1.5.1": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.test-framework@7056e7f856e9", "fingerprint": "7056e7f856e91f1d294fbe8db0c8fea1ffd9ad5d", "editorCompatibility": "2022.3.0a1", "version": "1.5.1", "source": "builtin", "testable": false}, "com.unity.test-framework.performance@3.1.0": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed", "fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50", "editorCompatibility": "2020.3.0a1", "version": "3.1.0", "source": "registry", "testable": false}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry", "testable": false}, "com.unity.transport@2.3.0": {"name": "com.unity.transport", "displayName": "Unity Transport", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.transport@ac326714c170", "fingerprint": "ac326714c1709ed1bffcf14fc375314346e8e665", "editorCompatibility": "2022.3.0a1", "version": "2.3.0", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.ugui@57cef44123c7", "fingerprint": "57cef44123c7486b2e2f08dc6535aecf6c34b8ef", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.visualscripting@1.9.7": {"name": "com.unity.visualscripting", "displayName": "Visual Scripting", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485", "fingerprint": "6279e2b7c4858e56cca7f367cd38c49ef66778c9", "editorCompatibility": "2021.3.0a1", "version": "1.9.7", "source": "registry", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.12f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.searcher@4.9.3": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.searcher@1e17ce91558d", "fingerprint": "1e17ce91558d1d9127554adc03d275f39a7466a2", "editorCompatibility": "2019.1.0a1", "version": "4.9.3", "source": "registry", "testable": false}, "com.unity.visualeffectgraph@17.1.0": {"name": "com.unity.visualeffectgraph", "displayName": "Visual Effect Graph", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.visualeffectgraph@23880e440107", "fingerprint": "23880e440107f37bfb426ad232c9011e5a1459f2", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.high-definition-config@17.1.0": {"name": "com.unity.render-pipelines.high-definition-config", "displayName": "High Definition Render Pipeline Config", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.render-pipelines.high-definition-config@8c699487ee0c", "fingerprint": "8c699487ee0c5bb938902fb2e91075c921a1fb5f", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.editorcoroutines@1.0.0": {"name": "com.unity.editorcoroutines", "displayName": "Editor Coroutines", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.editorcoroutines@7d48783e7b8c", "fingerprint": "7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca", "editorCompatibility": "2018.1.0a1", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.performance.profile-analyzer@1.2.3": {"name": "com.unity.performance.profile-analyzer", "displayName": "Profile Analyzer", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.performance.profile-analyzer@a68e7bc84997", "fingerprint": "a68e7bc849973d943853204178d08a2bc7656ffe", "editorCompatibility": "2020.3.0a1", "version": "1.2.3", "source": "registry", "testable": false}, "com.unity.testtools.codecoverage@1.2.6": {"name": "com.unity.testtools.codecoverage", "displayName": "Code Coverage", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39", "fingerprint": "205a02cbcb39584f20b51c49b853047aceb3a3a7", "editorCompatibility": "2019.3.0a1", "version": "1.2.6", "source": "registry", "testable": false}, "com.unity.settings-manager@2.1.0": {"name": "com.unity.settings-manager", "displayName": "Settings Manager", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out demo\\Library\\PackageCache\\com.unity.settings-manager@41738c275190", "fingerprint": "41738c27519039c335849eb78949382f4d7a3544", "editorCompatibility": "2022.3.0a1", "version": "2.1.0", "source": "registry", "testable": false}}}