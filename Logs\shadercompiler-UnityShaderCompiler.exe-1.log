Base path: 'C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data', plugins path 'C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBuildPrefixSum_128 ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BUILD_PREFIX_SUM=CSVFXBuildPrefixSum_128>,<VFX_USE_INSTANCING=1>,<PREFIX_SUM_THREAD_COUNT=128>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=8116

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBuildPrefixSum ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BUILD_PREFIX_SUM=CSVFXBuildPrefixSum>,<VFX_USE_INSTANCING=1>,<PREFIX_SUM_THREAD_COUNT=1024>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=1728

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBatchSumCount_128 ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BATCH_COUNT=CSVFXBatchSumCount_128>,<PREFIX_SUM_THREAD_COUNT=128>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=1586

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBatchSumCount ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BATCH_COUNT=CSVFXBatchSumCount>,<PREFIX_SUM_THREAD_COUNT=1024>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=758

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXPrepareSingleInstance ppOnly=0 stripLineD=0 buildPlatform=19 km=<PREFIX_SUM_THREAD_COUNT=1>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=686

Cmd: compileSnippet
  insize=11898 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Lit/Lit.shader name=HDRP/Lit pass=DepthOnly ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=_NORMALMAP_TANGENT_SPACE dKW=WRITE_MSAA_DEPTH DOTS_INSTANCING_ON LOD_FADE_CROSSFADE WRITE_NORMAL_BUFFER WRITE_DECAL_BUFFER WRITE_RENDERING_LAYER INSTANCING_ON _PIXEL_DISPLACEMENT_LOCK_OBJECT_SCALE _EMISSIVE_MAPPING_PLANAR _EMISSIVE_MAPPING_TRIPLANAR _EMISSIVE_MAPPING_BASE _ENABLESPECULAROCCLUSION _SPECULAR_OCCLUSION_NONE _SPECULAR_OCCLUSION_FROM_BENT_NORMAL_MAP _MATERIAL_FEATURE_CLEAR_COAT _ENABLE_GEOMETRIC_SPECULAR_AA _MASKMAP _DEPTHOFFSET_ON _DOUBLESIDED_ON _VERTEX_DISPLACEMENT _PIXEL_DISPLACEMENT _DISPLACEMENT_LOCK_TILING_SCALE _MAPPING_PLANAR _MAPPING_TRIPLANAR _REQUIRE_UV2 _REQUIRE_UV3 _HEIGHTMAP _DISABLE_DECALS _NORMALMAP _ALPHATEST_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Fragment platform=d3d11 reqs=1101803 mask=6 start=657 ok=1 outsize=174

Cmd: compileComputeKernel
  insize=13256 file=Packages/com.unity.render-pipelines.high-definition/Runtime/PostProcessing/Shaders/LutBuilder3D.compute kernel=KBuild ppOnly=0 stripLineD=0 buildPlatform=19 km=<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=TONEMAPPING_NONE GRADE_IN_ACESCG SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=8936

