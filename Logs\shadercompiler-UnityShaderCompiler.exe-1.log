Base path: 'C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data', plugins path 'C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBuildPrefixSum_128 ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BUILD_PREFIX_SUM=CSVFXBuildPrefixSum_128>,<VFX_USE_INSTANCING=1>,<PREFIX_SUM_THREAD_COUNT=128>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=8116

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBuildPrefixSum ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BUILD_PREFIX_SUM=CSVFXBuildPrefixSum>,<VFX_USE_INSTANCING=1>,<PREFIX_SUM_THREAD_COUNT=1024>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=1728

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBatchSumCount_128 ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BATCH_COUNT=CSVFXBatchSumCount_128>,<PREFIX_SUM_THREAD_COUNT=128>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=1586

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXBatchSumCount ppOnly=0 stripLineD=0 buildPlatform=19 km=<VFX_BATCH_COUNT=CSVFXBatchSumCount>,<PREFIX_SUM_THREAD_COUNT=1024>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=758

Cmd: compileComputeKernel
  insize=4405 file=Packages/com.unity.visualeffectgraph/Shaders/VFXPrefixSum.compute kernel=CSVFXPrepareSingleInstance ppOnly=0 stripLineD=0 buildPlatform=19 km=<PREFIX_SUM_THREAD_COUNT=1>,<UNITY_VERSION=60010012> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=686

